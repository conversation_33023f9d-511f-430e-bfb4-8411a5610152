import os
import tempfile
import logging
import time
from typing import List, Dict, Any, <PERSON><PERSON>
import speech_recognition as sr
from moviepy.editor import VideoFileClip, concatenate_videoclips, AudioFileClip, CompositeVideoClip, TextClip, ImageClip
from PIL import Image, ImageDraw, ImageFont
import openai
from pydub import AudioSegment
import shutil
import traceback
import cv2
import subprocess
import numpy as np

logger = logging.getLogger(__name__)


# def add_watermark(video_path: str, output_path: str, watermark_text: str):
#     """
#     Adds a text watermark to a video, covering the whole screen with a faded effect,
#     using OpenCV for video processing and FFmpeg to re-attach the original audio.
#     """
#     temp_video_output = None  # Path for the video-only watermarked file

#     try:
#         logger.info(
#             f"Attempting to add full-screen watermark to {video_path} (OpenCV method)")

#         # 1. Open the video file with OpenCV
#         cap = cv2.VideoCapture(video_path)
#         if not cap.isOpened():
#             raise IOError(f"Cannot open video file: {video_path}")

#         # 2. Get original video properties
#         frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
#         frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
#         fps = cap.get(cv2.CAP_PROP_FPS)

#         # --- Defensive FPS check ---
#         if fps is None or fps == 0:
#             logger.warning(
#                 f"Video file {video_path} has no FPS data. Defaulting to 24 FPS.")
#             fps = 24

#         # 3. Define the video writer for the temporary (video-only) file
#         temp_dir = os.path.dirname(output_path)
#         temp_video_output = os.path.join(
#             temp_dir, f"temp_video_{os.path.basename(output_path)}")
#         fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # Codec for .mp4 files
#         out = cv2.VideoWriter(temp_video_output, fourcc,
#                               fps, (frame_width, frame_height))

#         # 4. Process each frame: read, watermark, and write
#         while True:
#             ret, frame = cap.read()
#             if not ret:
#                 break  # End of video

#             # --- Add full-screen watermark text using OpenCV ---
#             # Define font, size, color, and position
#             font_face = cv2.FONT_HERSHEY_SIMPLEX
#             font_scale = 1.0  # Adjust for desired text size
#             font_color = (255, 255, 255)  # White
#             thickness = 2
#             # Adjust for desired transparency (0.0 - 1.0) - Faded effect
#             alpha = 0.15

#             # Calculate text size
#             text_size, _ = cv2.getTextSize(
#                 watermark_text, font_face, font_scale, thickness)
#             text_width, text_height = text_size

#             # Calculate the number of times the watermark needs to be repeated
#             # to cover the entire frame.  Adjust spacing slightly.
#             x_step = text_width + 50  # Horizontal spacing
#             y_step = text_height + 50  # Vertical spacing

#             # Loop to cover the entire frame with the watermark
#             for y in range(0, frame_height, y_step):
#                 for x in range(0, frame_width, x_step):
#                     # Create an overlay to apply transparency
#                     overlay = frame.copy()
#                     cv2.putText(overlay, watermark_text, (x, y + text_height),
#                                 font_face, font_scale, font_color, thickness, cv2.LINE_AA)

#                     # Blend the watermark with the frame
#                     cv2.addWeighted(overlay, alpha, frame, 1 - alpha, 0, frame)

#             out.write(frame)

#         # Release the video objects
#         cap.release()
#         out.release()
#         logger.info(
#             f"Created temporary watermarked video at {temp_video_output}")

#         # 5. --- Combine the watermarked video with original audio using FFmpeg ---
#         logger.info(
#             "Combining watermarked video with original audio using FFmpeg...")

#         ffmpeg_command = [
#             'ffmpeg',
#             '-i', temp_video_output,
#             '-i', video_path,
#             '-c:v', 'copy',
#             '-map', '0:v:0',
#             '-map', '1:a:0?',
#             '-c:a', 'aac',
#             '-shortest',
#             '-y',
#             output_path
#         ]

#         # Execute the command. Using check=True will raise an error if FFmpeg fails.
#         subprocess.run(ffmpeg_command, check=True,
#                        capture_output=True, text=True)

#         logger.info(
#             f"Successfully watermarked video with audio saved to {output_path}")

#     except Exception as e:
#         # If anything fails, log the error and copy the original file
#         logger.error(
#             f"Failed to add watermark (OpenCV method): {e}\n{traceback.format_exc()}")
#         logger.warning(
#             f"Watermark failed. Using original, non-watermarked video for {output_path}")
#         if os.path.exists(video_path):
#             shutil.copy(video_path, output_path)
#     finally:
#         # Clean up the temporary video file
#         if temp_video_output and os.path.exists(temp_video_output):
#             os.remove(temp_video_output)

def add_watermark(video_path: str, output_path: str, watermark_text: str):
    """
    Adds a text watermark using an OPTIMIZED OpenCV method.
    The watermark layer is created once, then blended with each frame.
    """
    temp_video_output = None
    try:
        logger.info(
            f"Adding watermark to {video_path} (Optimized OpenCV method)")

        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise IOError(f"Cannot open video file: {video_path}")

        frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS) or 24

        temp_dir = os.path.dirname(output_path)
        temp_video_output = os.path.join(
            temp_dir, f"temp_video_{os.path.basename(output_path)}")
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(temp_video_output, fourcc,
                              fps, (frame_width, frame_height))

        # --- THE OPTIMIZATION: Create the watermark layer ONCE ---
        watermark_layer = np.zeros(
            (frame_height, frame_width, 3), dtype=np.uint8)
        font_face = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 1.0
        font_color = (255, 255, 255)  # White
        thickness = 2

        text_size, _ = cv2.getTextSize(
            watermark_text, font_face, font_scale, thickness)
        text_width, text_height = text_size
        x_step = text_width + 50
        y_step = text_height + 50

        # Draw the grid onto our blank watermark_layer
        for y in range(0, frame_height, y_step):
            for x in range(0, frame_width, x_step):
                cv2.putText(watermark_layer, watermark_text, (x, y + text_height),
                            font_face, font_scale, font_color, thickness, cv2.LINE_AA)
        # --- Watermark layer is now ready ---

        # Process each frame
        while True:
            ret, frame = cap.read()
            if not ret:
                break

            # --- Blend the pre-made layer with the current frame ---
            # This is much faster than the old double-loop
            alpha = 0.15
            cv2.addWeighted(watermark_layer, alpha, frame, 1.0, 0, frame)

            out.write(frame)

        cap.release()
        out.release()
        logger.info(
            f"Created temporary watermarked video at {temp_video_output}")

        # Combine with audio using FFmpeg
        logger.info("Combining with original audio using FFmpeg...")
        ffmpeg_command = [
            'ffmpeg', '-y', '-i', temp_video_output, '-i', video_path,
            '-c:v', 'copy', '-c:a', 'aac', '-map', '0:v:0', '-map', '1:a:0?', '-shortest', output_path
        ]
        subprocess.run(ffmpeg_command, check=True,
                       capture_output=True, text=True)
        logger.info(f"Successfully watermarked video saved to {output_path}")

    except Exception as e:
        logger.error(
            f"Failed to add watermark (Optimized OpenCV): {e}\n{traceback.format_exc()}")
        logger.warning(
            f"Watermark failed. Using original video for {output_path}")
        if os.path.exists(video_path):
            shutil.copy(video_path, output_path)
    finally:
        if temp_video_output and os.path.exists(temp_video_output):
            os.remove(temp_video_output)


def transcribe_video(video_path: str, temp_dir: str) -> Tuple[str, List[Dict[str, float]]]:
    """Transcribe video and return transcript with timestamps"""
    video = None
    audio_path = None

    try:
        # Extract audio from video
        video = VideoFileClip(video_path)

        audio_path = os.path.join(temp_dir, "audio_extract.wav")

        # Extract audio with better settings using pydub
        video.audio.write_audiofile(
            audio_path,
            codec='pcm_s16le',
            ffmpeg_params=["-ac", "1", "-ar", "16000"],  # Mono, 16kHz
            verbose=False
        )

        # Process audio with pydub
        audio = AudioSegment.from_wav(audio_path)

        # Split audio into chunks of 30 seconds
        chunk_length_ms = 30000  # 30 seconds
        chunks = [audio[i:i + chunk_length_ms]
                  for i in range(0, len(audio), chunk_length_ms)]

        transcript = ""
        timestamps = []
        current_time = 0

        # Initialize recognizer with optimized settings
        r = sr.Recognizer()
        r.energy_threshold = 300
        r.dynamic_energy_threshold = True
        r.pause_threshold = 0.8

        # Process each chunk
        for i, chunk in enumerate(chunks):
            # Export chunk to temporary file
            chunk_path = os.path.join(temp_dir, f"chunk_{i}.wav")
            chunk.export(chunk_path, format="wav")
            try:
                with sr.AudioFile(chunk_path) as source:
                    # Adjust for ambient noise
                    r.adjust_for_ambient_noise(source, duration=0.5)
                    audio_data = r.record(source)

                    # Try to recognize the chunk
                    try:
                        result = r.recognize_google(
                            audio_data,
                            language="en-US",
                            show_all=True
                        )

                        if result and 'alternative' in result and result['alternative']:
                            chunk_transcript = result['alternative'][0]['transcript']

                            # Calculate timestamps for this chunk
                            # Convert ms to seconds
                            chunk_duration = len(chunk) / 1000.0
                            words = chunk_transcript.split()
                            time_per_word = chunk_duration
                            len(words) if words else 0

                            for word in words:
                                timestamps.append({
                                    "word": word,
                                    "start": round(current_time, 2),
                                    "end": round(current_time + time_per_word, 2)
                                })
                                current_time += time_per_word

                            if transcript:
                                transcript += " " + chunk_transcript
                            else:
                                transcript = chunk_transcript

                    except sr.UnknownValueError:
                        logger.warning(
                            f"Could not understand audio in chunk {i}")
                    except sr.RequestError as e:
                        logger.error(f"Error in chunk {i}: {str(e)}")

            finally:
                # Clean up chunk file
                if os.path.exists(chunk_path):
                    os.remove(chunk_path)

        return transcript, timestamps

    except Exception as e:
        logger.error(f"Error in transcribe_video: {str(e)}")
        return "", []
    finally:
        # Clean up audio file
        if audio_path and os.path.exists(audio_path):
            try:
                os.remove(audio_path)
            except Exception as e:
                logger.error(f"Error removing audio file: {str(e)}")

        # Ensure video is closed
        if video is not None:
            try:
                video.reader.close()
                if video.audio:
                    video.audio.reader.close_proc()
                del video
            except Exception as e:
                logger.error(f"Error cleaning up video object: {str(e)}")


def segment_transcript(
    transcript: str,
    timestamps: List[Dict[str, float]],
    min_duration: float = 10.0,
    max_duration: float = 60.0,
    refine_with_ai: bool = False
) -> List[Dict[str, Any]]:
    """
    Segment transcript into coherent parts using AI-powered content analysis.
    max_duration is treated as a maximum limit, not a target duration.
    """
    try:
        # Validate inputs
        if not transcript or not timestamps:
            logger.warning("Empty transcript or timestamps provided")
            return []

        if min_duration <= 0 or max_duration <= 0:
            logger.error(f"Invalid duration parameters: min={min_duration}, max={max_duration}")
            raise ValueError("Duration parameters must be positive")

        if min_duration >= max_duration:
            logger.error(f"Invalid duration range: min={min_duration} >= max={max_duration}")
            raise ValueError("min_duration must be less than max_duration")

        # Validate timestamps structure
        for i, ts in enumerate(timestamps):
            if not isinstance(ts, dict) or 'start' not in ts or 'end' not in ts:
                logger.error(f"Invalid timestamp at index {i}: {ts}")
                raise ValueError(f"Invalid timestamp structure at index {i}")
            if ts['end'] <= ts['start']:
                logger.warning(f"Invalid timestamp duration at index {i}: start={ts['start']}, end={ts['end']}")

        # If AI refinement is enabled, use intelligent segmentation
        if refine_with_ai and os.getenv("OPENAI_API_KEY"):
            segments = _ai_powered_segmentation(transcript, timestamps, min_duration, max_duration)
        else:
            # Fallback to basic segmentation
            segments = _basic_content_segmentation(timestamps, min_duration, max_duration)

        # Validate output segments
        valid_segments = []
        for i, segment in enumerate(segments):
            if not isinstance(segment, dict) or 'start' not in segment or 'end' not in segment:
                logger.error(f"Invalid segment structure at index {i}: {segment}")
                continue

            duration = segment['end'] - segment['start']
            if duration <= 0:
                logger.error(f"Invalid segment duration at index {i}: {duration:.2f}s")
                continue

            if duration < min_duration:
                logger.warning(f"Segment {i} below minimum duration: {duration:.2f}s < {min_duration}s")
                continue

            if duration > max_duration:
                logger.warning(f"Segment {i} above maximum duration: {duration:.2f}s > {max_duration}s")
                # Truncate to max_duration
                segment['end'] = segment['start'] + max_duration

            valid_segments.append(segment)

        logger.info(f"Generated {len(valid_segments)} valid segments from {len(segments)} total segments")
        return valid_segments

    except Exception as e:
        logger.error(f"Error in segment_transcript: {str(e)}")
        raise


def _ai_powered_segmentation(
    transcript: str,
    timestamps: List[Dict[str, float]],
    min_duration: float,
    max_duration: float
) -> List[Dict[str, Any]]:
    """Use AI to identify optimal clip segments based on content virality and engagement potential"""
    try:
        openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        # Enhanced AI prompt for intelligent segmentation
        prompt = (
            f"Analyze this video transcript and identify the most engaging segments for short-form content:\n\n"
            f"TRANSCRIPT:\n{transcript}\n\n"
            f"REQUIREMENTS:\n"
            f"- Each segment should be {min_duration}-{max_duration} seconds long\n"
            f"- Prioritize segments with high virality potential (hooks, punchlines, key insights)\n"
            f"- Look for natural content breaks (topic changes, pauses, conclusions)\n"
            f"- Shorter clips (15-30s) are preferred when content is punchy\n"
            f"- Longer clips (30-{max_duration}s) only when content requires context\n\n"
            f"Return a JSON array of segments with this exact format:\n"
            f"[\n"
            f'  {{"start_sentence": "exact sentence text", "end_sentence": "exact sentence text", "reason": "why this segment is engaging", "optimal_duration": 25}}\n'
            f"]\n\n"
            f"Focus on quality over quantity. Return 3-7 of the BEST segments only."
        )

        response = openai_client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are an expert video editor who creates viral short-form content. You understand what makes clips engaging and shareable."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3
        )

        # Parse AI response and map to timestamps
        ai_segments = _parse_ai_segments_response(response.choices[0].message.content, transcript, timestamps)

        if ai_segments:
            logger.info(f"AI identified {len(ai_segments)} optimal segments")
            return ai_segments
        else:
            logger.warning("AI segmentation failed, falling back to basic segmentation")
            return _basic_content_segmentation(timestamps, min_duration, max_duration)

    except Exception as e:
        logger.error(f"Error in AI segmentation: {str(e)}")
        return _basic_content_segmentation(timestamps, min_duration, max_duration)


def _basic_content_segmentation(
    timestamps: List[Dict[str, float]],
    min_duration: float,
    max_duration: float
) -> List[Dict[str, Any]]:
    """Basic segmentation that looks for natural breaks and optimal clip lengths"""
    if not timestamps:
        return []

    segments = []
    current_segment_words = []
    segment_start_time = timestamps[0]["start"]

    for i, ts in enumerate(timestamps):
        current_segment_words.append(ts["word"])
        segment_end_time = ts["end"]
        current_duration = segment_end_time - segment_start_time

        # Validate current duration
        if current_duration < 0:
            logger.warning(f"Negative duration detected at timestamp {i}: {current_duration}")
            continue

        # Look for natural break points (sentence endings, pauses)
        is_sentence_end = ts["word"].rstrip().endswith(('.', '!', '?'))
        is_long_pause = (i < len(timestamps) - 1 and
                        timestamps[i + 1]["start"] - ts["end"] > 1.0)  # 1 second pause

        # Determine if we should end the segment
        should_end_segment = False

        if current_duration >= max_duration:
            # Must end - we've hit the maximum
            should_end_segment = True
        elif current_duration >= min_duration:
            # We can end if we find a good break point
            if is_sentence_end or is_long_pause:
                should_end_segment = True
            # For shorter clips (15-25s), prefer to end early if content is punchy
            elif current_duration >= 15 and _is_punchy_content(" ".join(current_segment_words)):
                should_end_segment = True
        elif i == len(timestamps) - 1:
            # Last segment - include if it meets minimum duration
            should_end_segment = current_duration >= min_duration

        if should_end_segment:
            # Validate segment before adding
            if current_duration > 0 and segment_start_time < segment_end_time:
                segments.append({
                    "text": " ".join(current_segment_words),
                    "start": segment_start_time,
                    "end": segment_end_time
                })
            else:
                logger.warning(f"Skipping invalid segment: start={segment_start_time}, end={segment_end_time}, duration={current_duration}")

            # Reset for next segment
            current_segment_words = []
            if i + 1 < len(timestamps):
                segment_start_time = timestamps[i + 1]["start"]

    return segments


def _is_punchy_content(text: str) -> bool:
    """Simple heuristic to identify punchy, engaging content"""
    punchy_indicators = [
        '!', '?', 'wow', 'amazing', 'incredible', 'shocking', 'secret',
        'trick', 'hack', 'tip', 'mistake', 'wrong', 'right', 'best', 'worst'
    ]
    text_lower = text.lower()
    return any(indicator in text_lower for indicator in punchy_indicators)


def _parse_ai_segments_response(
    ai_response: str,
    transcript: str,
    timestamps: List[Dict[str, float]]
) -> List[Dict[str, Any]]:
    """Parse AI response and map sentences to actual timestamps"""
    try:
        import json
        import re

        # Extract JSON from response
        json_match = re.search(r'\[.*\]', ai_response, re.DOTALL)
        if not json_match:
            return []

        ai_segments = json.loads(json_match.group())
        result_segments = []

        # Create a mapping of words to timestamps for easier lookup
        word_to_timestamp = {}
        for ts in timestamps:
            word_to_timestamp[ts["word"].lower().strip('.,!?')] = ts

        for segment in ai_segments:
            start_sentence = segment.get("start_sentence", "")
            end_sentence = segment.get("end_sentence", "")

            # Find start and end times by matching sentences to timestamps
            start_time = _find_sentence_timestamp(start_sentence, timestamps, word_to_timestamp, is_start=True)
            end_time = _find_sentence_timestamp(end_sentence, timestamps, word_to_timestamp, is_start=False)

            if start_time is not None and end_time is not None and end_time > start_time:
                # Extract the actual text for this time range
                segment_words = [ts["word"] for ts in timestamps
                               if start_time <= ts["start"] <= end_time]

                result_segments.append({
                    "text": " ".join(segment_words),
                    "start": start_time,
                    "end": end_time
                })

        return result_segments

    except Exception as e:
        logger.error(f"Error parsing AI segments: {str(e)}")
        return []


def _find_sentence_timestamp(
    sentence: str,
    timestamps: List[Dict[str, float]],
    word_to_timestamp: Dict[str, Dict],
    is_start: bool = True
) -> float:
    """Find the timestamp for a given sentence"""
    try:
        # Clean and split sentence into words
        words = sentence.lower().strip('.,!?').split()
        if not words:
            return None

        # Look for the first few words of the sentence in timestamps
        search_words = words[:3]  # Use first 3 words for matching

        for i, ts in enumerate(timestamps):
            ts_word = ts["word"].lower().strip('.,!?')
            if ts_word == search_words[0]:
                # Check if subsequent words match
                match_count = 1
                for j, search_word in enumerate(search_words[1:], 1):
                    if i + j < len(timestamps):
                        next_ts_word = timestamps[i + j]["word"].lower().strip('.,!?')
                        if next_ts_word == search_word:
                            match_count += 1
                        else:
                            break

                # If we matched at least 2 words, consider it a match
                if match_count >= min(2, len(search_words)):
                    return ts["start"] if is_start else timestamps[min(i + match_count - 1, len(timestamps) - 1)]["end"]

        return None

    except Exception as e:
        logger.error(f"Error finding sentence timestamp: {str(e)}")
        return None


def clip_video_from_text(
    video_path: str,
    segments: List[Dict[str, Any]],
    output_dir: str
) -> List[str]:
    """Clip video based on transcript segments"""
    try:
        output_paths = []

        # Get video duration to validate segments
        video_duration = None
        try:
            with VideoFileClip(video_path) as video:
                video_duration = video.duration
        except Exception as e:
            logger.error(f"Error getting video duration: {str(e)}")
            # If we can't get duration, we'll still try to process segments
            video_duration = float('inf')

        for i, segment in enumerate(segments):
            # Validate segment data
            if not isinstance(segment.get("start"), (int, float)) or not isinstance(segment.get("end"), (int, float)):
                logger.error(f"Invalid segment {i}: start={segment.get('start')}, end={segment.get('end')}")
                continue

            # Ensure start and end times are within video duration
            start_time = max(0, float(segment["start"]))
            end_time = min(float(segment["end"]), video_duration) if video_duration != float('inf') else float(segment["end"])

            # Skip invalid segments
            if end_time <= start_time:
                logger.error(f"Invalid segment {i}: end_time ({end_time}) <= start_time ({start_time})")
                continue

            # Validate minimum duration
            segment_duration = end_time - start_time
            if segment_duration < 1.0:  # Minimum 1 second
                logger.error(f"Segment {i} too short: {segment_duration:.2f}s")
                continue

            # Save clip with audio
            output_path = os.path.join(
                output_dir, f"clip_{i}_{os.path.basename(video_path)}")

            # Use ffmpeg directly to ensure proper audio handling
            import subprocess
            cmd = [
                'ffmpeg',
                '-i', video_path,
                '-ss', str(start_time),
                '-to', str(end_time),
                '-c:v', 'libx264',
                '-c:a', 'aac',
                '-strict', '-2',
                '-preset', 'ultrafast',
                '-threads', '4',
                '-ac', '2',
                '-ar', '44100',
                '-b:a', '192k',
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode != 0:
                logger.error(f"Error creating clip {i}: {result.stderr}")
                continue

            # Verify the output file
            if not os.path.exists(output_path) or os.path.getsize(output_path) == 0:
                logger.error(
                    f"Output file {output_path} is empty or does not exist")
                continue

            # Check video and audio streams
            probe_result = subprocess.run([
                'ffprobe',
                '-v', 'error',
                '-show_entries', 'stream=codec_type,codec_name',
                '-of', 'json',
                output_path
            ], capture_output=True, text=True)

            if 'audio' not in probe_result.stdout:
                logger.error(f"Output clip {output_path} has no audio track")
                continue

            logger.info(f"Successfully created clip: {output_path}")
            output_paths.append(output_path)

        return output_paths
    except Exception as e:
        logger.error(f"Error in clip_video_from_text: {str(e)}")
        raise


def trim_video(input_path: str, output_path: str, start_time: float, end_time: float) -> str:
    """Trim video to specified start and end times"""
    video = None
    trimmed = None
    try:
        video = VideoFileClip(input_path)
        trimmed = video.subclip(start_time, end_time)
        trimmed.write_videofile(output_path)
        return output_path
    except Exception as e:
        logger.error(f"Error in trim_video: {str(e)}")
        raise
    finally:
        if trimmed is not None:
            try:
                del trimmed
            except Exception as e:
                logger.error(f"Error deleting trimmed video: {str(e)}")
        if video is not None:
            try:
                del video
            except Exception as e:
                logger.error(f"Error deleting video: {str(e)}")


def adjust_speed(input_path: str, output_path: str, speed_factor: float) -> str:
    """Adjust video speed"""
    video = None
    modified = None
    try:
        video = VideoFileClip(input_path)
        modified = video.fx(VideoFileClip.speedx, speed_factor)
        modified.write_videofile(output_path)
        return output_path
    except Exception as e:
        logger.error(f"Error in adjust_speed: {str(e)}")
        raise
    finally:
        if modified is not None:
            try:
                del modified
            except Exception as e:
                logger.error(f"Error deleting modified video: {str(e)}")
        if video is not None:
            try:
                del video
            except Exception as e:
                logger.error(f"Error deleting video: {str(e)}")


def crop_video(input_path: str, output_path: str, x1: int, y1: int, x2: int, y2: int) -> str:
    """Crop video to specified rectangle"""
    video = None
    cropped = None
    try:
        video = VideoFileClip(input_path)
        cropped = video.crop(x1=x1, y1=y1, x2=x2, y2=y2)
        cropped.write_videofile(output_path)
        return output_path
    except Exception as e:
        logger.error(f"Error in crop_video: {str(e)}")
        raise
    finally:
        if cropped is not None:
            try:
                del cropped
            except Exception as e:
                logger.error(f"Error deleting cropped video: {str(e)}")
        if video is not None:
            try:
                del video
            except Exception as e:
                logger.error(f"Error deleting video: {str(e)}")


def rotate_video(input_path: str, output_path: str, angle: int) -> str:
    """Rotate video by specified angle"""
    video = None
    rotated = None
    try:
        video = VideoFileClip(input_path)
        rotated = video.rotate(angle)
        rotated.write_videofile(output_path)
        return output_path
    except Exception as e:
        logger.error(f"Error in rotate_video: {str(e)}")
        raise
    finally:
        if rotated is not None:
            try:
                del rotated
            except Exception as e:
                logger.error(f"Error deleting rotated video: {str(e)}")
        if video is not None:
            try:
                del video
            except Exception as e:
                logger.error(f"Error deleting video: {str(e)}")


def merge_videos(input_paths: List[str], output_path: str) -> str:
    """Merge multiple videos"""
    clips = []
    final_clip = None
    try:
        clips = [VideoFileClip(path) for path in input_paths]
        final_clip = concatenate_videoclips(clips)
        final_clip.write_videofile(output_path)
        return output_path
    except Exception as e:
        logger.error(f"Error in merge_videos: {str(e)}")
        raise
    finally:
        if final_clip is not None:
            try:
                del final_clip
            except Exception as e:
                logger.error(f"Error deleting final clip: {str(e)}")
        for clip in clips:
            try:
                del clip
            except Exception as e:
                logger.error(f"Error deleting clip: {str(e)}")


def segment_video_uniformly(video_path: str, min_duration: int, max_duration: int) -> List[Dict]:
    """Create segments of fixed length if transcription fails"""
    video = None
    try:
        video = VideoFileClip(video_path)
        duration = video.duration
        # fallback clip length between limits
        segment_length = max(min_duration, min(30, max_duration))

        segments = []
        start = 0
        while start < duration:
            end = min(start + segment_length, duration)
            segments.append({
                "start": start,
                "end": end,
                "text": f"Clip from {start:.2f}s to {end:.2f}s"
            })
            start = end

        return segments
    except Exception as e:
        logger.error(f"Error in segment_video_uniformly: {str(e)}")
        raise
    finally:
        if video is not None:
            try:
                del video
            except Exception as e:
                logger.error(f"Error deleting video object: {str(e)}")

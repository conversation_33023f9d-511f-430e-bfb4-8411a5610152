import os
import tempfile
import logging
import time
from typing import List, Dict, Any, <PERSON><PERSON>
import speech_recognition as sr
from moviepy.editor import VideoFileClip, concatenate_videoclips, AudioFileClip, CompositeVideoClip, TextClip, ImageClip
from PIL import Image, ImageDraw, ImageFont
import openai
from pydub import AudioSegment
import shutil
import traceback
import cv2
import subprocess
import numpy as np

logger = logging.getLogger(__name__)


# def add_watermark(video_path: str, output_path: str, watermark_text: str):
#     """
#     Adds a text watermark to a video, covering the whole screen with a faded effect,
#     using OpenCV for video processing and FFmpeg to re-attach the original audio.
#     """
#     temp_video_output = None  # Path for the video-only watermarked file

#     try:
#         logger.info(
#             f"Attempting to add full-screen watermark to {video_path} (OpenCV method)")

#         # 1. Open the video file with OpenCV
#         cap = cv2.VideoCapture(video_path)
#         if not cap.isOpened():
#             raise IOError(f"Cannot open video file: {video_path}")

#         # 2. Get original video properties
#         frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
#         frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
#         fps = cap.get(cv2.CAP_PROP_FPS)

#         # --- Defensive FPS check ---
#         if fps is None or fps == 0:
#             logger.warning(
#                 f"Video file {video_path} has no FPS data. Defaulting to 24 FPS.")
#             fps = 24

#         # 3. Define the video writer for the temporary (video-only) file
#         temp_dir = os.path.dirname(output_path)
#         temp_video_output = os.path.join(
#             temp_dir, f"temp_video_{os.path.basename(output_path)}")
#         fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # Codec for .mp4 files
#         out = cv2.VideoWriter(temp_video_output, fourcc,
#                               fps, (frame_width, frame_height))

#         # 4. Process each frame: read, watermark, and write
#         while True:
#             ret, frame = cap.read()
#             if not ret:
#                 break  # End of video

#             # --- Add full-screen watermark text using OpenCV ---
#             # Define font, size, color, and position
#             font_face = cv2.FONT_HERSHEY_SIMPLEX
#             font_scale = 1.0  # Adjust for desired text size
#             font_color = (255, 255, 255)  # White
#             thickness = 2
#             # Adjust for desired transparency (0.0 - 1.0) - Faded effect
#             alpha = 0.15

#             # Calculate text size
#             text_size, _ = cv2.getTextSize(
#                 watermark_text, font_face, font_scale, thickness)
#             text_width, text_height = text_size

#             # Calculate the number of times the watermark needs to be repeated
#             # to cover the entire frame.  Adjust spacing slightly.
#             x_step = text_width + 50  # Horizontal spacing
#             y_step = text_height + 50  # Vertical spacing

#             # Loop to cover the entire frame with the watermark
#             for y in range(0, frame_height, y_step):
#                 for x in range(0, frame_width, x_step):
#                     # Create an overlay to apply transparency
#                     overlay = frame.copy()
#                     cv2.putText(overlay, watermark_text, (x, y + text_height),
#                                 font_face, font_scale, font_color, thickness, cv2.LINE_AA)

#                     # Blend the watermark with the frame
#                     cv2.addWeighted(overlay, alpha, frame, 1 - alpha, 0, frame)

#             out.write(frame)

#         # Release the video objects
#         cap.release()
#         out.release()
#         logger.info(
#             f"Created temporary watermarked video at {temp_video_output}")

#         # 5. --- Combine the watermarked video with original audio using FFmpeg ---
#         logger.info(
#             "Combining watermarked video with original audio using FFmpeg...")

#         ffmpeg_command = [
#             'ffmpeg',
#             '-i', temp_video_output,
#             '-i', video_path,
#             '-c:v', 'copy',
#             '-map', '0:v:0',
#             '-map', '1:a:0?',
#             '-c:a', 'aac',
#             '-shortest',
#             '-y',
#             output_path
#         ]

#         # Execute the command. Using check=True will raise an error if FFmpeg fails.
#         subprocess.run(ffmpeg_command, check=True,
#                        capture_output=True, text=True)

#         logger.info(
#             f"Successfully watermarked video with audio saved to {output_path}")

#     except Exception as e:
#         # If anything fails, log the error and copy the original file
#         logger.error(
#             f"Failed to add watermark (OpenCV method): {e}\n{traceback.format_exc()}")
#         logger.warning(
#             f"Watermark failed. Using original, non-watermarked video for {output_path}")
#         if os.path.exists(video_path):
#             shutil.copy(video_path, output_path)
#     finally:
#         # Clean up the temporary video file
#         if temp_video_output and os.path.exists(temp_video_output):
#             os.remove(temp_video_output)

def add_watermark(video_path: str, output_path: str, watermark_text: str):
    """
    Adds a text watermark using an OPTIMIZED OpenCV method.
    The watermark layer is created once, then blended with each frame.
    """
    temp_video_output = None
    try:
        logger.info(
            f"Adding watermark to {video_path} (Optimized OpenCV method)")

        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise IOError(f"Cannot open video file: {video_path}")

        frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS) or 24

        temp_dir = os.path.dirname(output_path)
        temp_video_output = os.path.join(
            temp_dir, f"temp_video_{os.path.basename(output_path)}")
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(temp_video_output, fourcc,
                              fps, (frame_width, frame_height))

        # --- THE OPTIMIZATION: Create the watermark layer ONCE ---
        watermark_layer = np.zeros(
            (frame_height, frame_width, 3), dtype=np.uint8)
        font_face = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 1.0
        font_color = (255, 255, 255)  # White
        thickness = 2

        text_size, _ = cv2.getTextSize(
            watermark_text, font_face, font_scale, thickness)
        text_width, text_height = text_size
        x_step = text_width + 50
        y_step = text_height + 50

        # Draw the grid onto our blank watermark_layer
        for y in range(0, frame_height, y_step):
            for x in range(0, frame_width, x_step):
                cv2.putText(watermark_layer, watermark_text, (x, y + text_height),
                            font_face, font_scale, font_color, thickness, cv2.LINE_AA)
        # --- Watermark layer is now ready ---

        # Process each frame
        while True:
            ret, frame = cap.read()
            if not ret:
                break

            # --- Blend the pre-made layer with the current frame ---
            # This is much faster than the old double-loop
            alpha = 0.15
            cv2.addWeighted(watermark_layer, alpha, frame, 1.0, 0, frame)

            out.write(frame)

        cap.release()
        out.release()
        logger.info(
            f"Created temporary watermarked video at {temp_video_output}")

        # Combine with audio using FFmpeg
        logger.info("Combining with original audio using FFmpeg...")
        ffmpeg_command = [
            'ffmpeg', '-y', '-i', temp_video_output, '-i', video_path,
            '-c:v', 'copy', '-c:a', 'aac', '-map', '0:v:0', '-map', '1:a:0?', '-shortest', output_path
        ]
        subprocess.run(ffmpeg_command, check=True,
                       capture_output=True, text=True)
        logger.info(f"Successfully watermarked video saved to {output_path}")

    except Exception as e:
        logger.error(
            f"Failed to add watermark (Optimized OpenCV): {e}\n{traceback.format_exc()}")
        logger.warning(
            f"Watermark failed. Using original video for {output_path}")
        if os.path.exists(video_path):
            shutil.copy(video_path, output_path)
    finally:
        if temp_video_output and os.path.exists(temp_video_output):
            os.remove(temp_video_output)


def transcribe_video(video_path: str, temp_dir: str) -> Tuple[str, List[Dict[str, float]]]:
    """Transcribe video and return transcript with timestamps"""
    video = None
    audio_path = None

    try:
        # Extract audio from video
        video = VideoFileClip(video_path)

        audio_path = os.path.join(temp_dir, "audio_extract.wav")

        # Extract audio with better settings using pydub
        video.audio.write_audiofile(
            audio_path,
            codec='pcm_s16le',
            ffmpeg_params=["-ac", "1", "-ar", "16000"],  # Mono, 16kHz
            verbose=False
        )

        # Process audio with pydub
        audio = AudioSegment.from_wav(audio_path)

        # Split audio into chunks of 30 seconds
        chunk_length_ms = 30000  # 30 seconds
        chunks = [audio[i:i + chunk_length_ms]
                  for i in range(0, len(audio), chunk_length_ms)]

        transcript = ""
        timestamps = []
        current_time = 0

        # Initialize recognizer with optimized settings
        r = sr.Recognizer()
        r.energy_threshold = 300
        r.dynamic_energy_threshold = True
        r.pause_threshold = 0.8

        # Process each chunk
        for i, chunk in enumerate(chunks):
            # Export chunk to temporary file
            chunk_path = os.path.join(temp_dir, f"chunk_{i}.wav")
            chunk.export(chunk_path, format="wav")
            try:
                with sr.AudioFile(chunk_path) as source:
                    # Adjust for ambient noise
                    r.adjust_for_ambient_noise(source, duration=0.5)
                    audio_data = r.record(source)

                    # Try to recognize the chunk
                    try:
                        result = r.recognize_google(
                            audio_data,
                            language="en-US",
                            show_all=True
                        )

                        if result and 'alternative' in result and result['alternative']:
                            chunk_transcript = result['alternative'][0]['transcript']

                            # Calculate timestamps for this chunk
                            # Convert ms to seconds
                            chunk_duration = len(chunk) / 1000.0
                            words = chunk_transcript.split()
                            time_per_word = chunk_duration
                            len(words) if words else 0

                            for word in words:
                                timestamps.append({
                                    "word": word,
                                    "start": round(current_time, 2),
                                    "end": round(current_time + time_per_word, 2)
                                })
                                current_time += time_per_word

                            if transcript:
                                transcript += " " + chunk_transcript
                            else:
                                transcript = chunk_transcript

                    except sr.UnknownValueError:
                        logger.warning(
                            f"Could not understand audio in chunk {i}")
                    except sr.RequestError as e:
                        logger.error(f"Error in chunk {i}: {str(e)}")

            finally:
                # Clean up chunk file
                if os.path.exists(chunk_path):
                    os.remove(chunk_path)

        return transcript, timestamps

    except Exception as e:
        logger.error(f"Error in transcribe_video: {str(e)}")
        return "", []
    finally:
        # Clean up audio file
        if audio_path and os.path.exists(audio_path):
            try:
                os.remove(audio_path)
            except Exception as e:
                logger.error(f"Error removing audio file: {str(e)}")

        # Ensure video is closed
        if video is not None:
            try:
                video.reader.close()
                if video.audio:
                    video.audio.reader.close_proc()
                del video
            except Exception as e:
                logger.error(f"Error cleaning up video object: {str(e)}")


def segment_transcript(
    transcript: str,
    timestamps: List[Dict[str, float]],
    min_duration: float = 10.0,
    max_duration: float = 60.0,
    refine_with_ai: bool = False
) -> List[Dict[str, Any]]:
    """Segment transcript into coherent parts"""
    try:
        if not transcript or not timestamps:
            return []

        # Create segments based on max_duration
        # segments = []
        # current_segment = {
        #     "text": "",
        #     "start": timestamps[0]["start"] if timestamps else 0,
        #     "end": 0,
        #     "words": []
        # }

        # # Group words into segments based on duration
        # for ts in timestamps:
        #     word = ts["word"]
        #     current_segment["words"].append(word)
        #     current_segment["end"] = ts["end"]

        #     # Check if we've reached max_duration
        #     segment_duration = current_segment["end"] - \
        #         current_segment["start"]
        #     if segment_duration >= max_duration:
        #         # Join words to form text
        #         current_segment["text"] = " ".join(current_segment["words"])
        #         segments.append({
        #             "text": current_segment["text"],
        #             "start": current_segment["start"],
        #             "end": current_segment["end"]
        #         })

        #         # Start new segment
        #         current_segment = {
        #             "text": "",
        #             "start": ts["end"],
        #             "end": ts["end"],
        #             "words": []
        #         }

        # # Add the last segment if it has content
        # if current_segment["words"]:
        #     current_segment["text"] = " ".join(current_segment["words"])
        #     segments.append({
        #         "text": current_segment["text"],
        #         "start": current_segment["start"],
        #         "end": current_segment["end"]
        #     })

        # # Filter out segments that are too short
        # segments = [s for s in segments if (
        #     s["end"] - s["start"]) >= min_duration]

        segments = []
        current_segment_words = []
        segment_start_time = timestamps[0]["start"]

        for i, ts in enumerate(timestamps):
            current_segment_words.append(ts["word"])
            segment_end_time = ts["end"]
            current_duration = segment_end_time - segment_start_time

            # Check if the current segment is within the desired range.
            # We also check if it's the last word to ensure the final segment is always added.
            if current_duration >= min_duration or i == len(timestamps) - 1:
                # If the segment is too long, we need to backtrack to fit max_duration.
                # This is a simple approach; more complex logic could find better split points.
                if current_duration > max_duration:
                    # For simplicity, we just cap it at max_duration for now.
                    # A more advanced version would find a sentence break.
                    segment_end_time = segment_start_time + max_duration
                    # In a real scenario, you'd adjust `current_segment_words` as well.

                # We have a valid segment.
                segments.append({
                    "text": " ".join(current_segment_words),
                    "start": segment_start_time,
                    "end": segment_end_time
                })

                # Reset for the next segment.
                current_segment_words = []
                # Avoid index error on the last element
                if i + 1 < len(timestamps):
                    segment_start_time = timestamps[i+1]["start"]

        # This final filter is a good safety net.
        final_segments = [s for s in segments if min_duration <= (
            s['end'] - s['start']) <= max_duration]

        # If enabled and OpenAI API key is available, refine segments with AI
        if refine_with_ai and os.getenv("OPENAI_API_KEY"):
            try:
                openai_client = openai.OpenAI(
                    api_key=os.getenv("OPENAI_API_KEY"))

                # Prepare the transcript for AI processing
                prompt = (
                    "The following is a video transcript. Please identify the most interesting "
                    "and coherent segments that would make good standalone clips:\n\n"
                    f"{transcript}\n\n"
                    "For each segment, provide the start and end sentences that mark the boundaries "
                    "of an interesting clip. List 3-5 segments in order of appearance."
                )

                response = openai_client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": "You are a video editor assistant that identifies the most interesting parts of a transcript."},
                        {"role": "user", "content": prompt}
                    ]
                )

                # Process AI response (simplified)
                ai_suggestions = response.choices[0].message.content
                logger.info(f"AI suggestions: {ai_suggestions}")

            except Exception as e:
                logger.error(f"Error refining with AI: {str(e)}")

        return final_segments
    except Exception as e:
        logger.error(f"Error in segment_transcript: {str(e)}")
        raise


def clip_video_from_text(
    video_path: str,
    segments: List[Dict[str, Any]],
    output_dir: str
) -> List[str]:
    """Clip video based on transcript segments"""
    try:
        output_paths = []

        for i, segment in enumerate(segments):
            # Ensure start and end times are within video duration
            start_time = max(0, segment["start"])
            end_time = min(segment["end"], segment["end"])

            # Skip invalid segments
            if end_time <= start_time:
                continue

            # Save clip with audio
            output_path = os.path.join(
                output_dir, f"clip_{i}_{os.path.basename(video_path)}")

            # Use ffmpeg directly to ensure proper audio handling
            import subprocess
            cmd = [
                'ffmpeg',
                '-i', video_path,
                '-ss', str(start_time),
                '-to', str(end_time),
                '-c:v', 'libx264',
                '-c:a', 'aac',
                '-strict', '-2',
                '-preset', 'ultrafast',
                '-threads', '4',
                '-ac', '2',
                '-ar', '44100',
                '-b:a', '192k',
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode != 0:
                logger.error(f"Error creating clip {i}: {result.stderr}")
                continue

            # Verify the output file
            if not os.path.exists(output_path) or os.path.getsize(output_path) == 0:
                logger.error(
                    f"Output file {output_path} is empty or does not exist")
                continue

            # Check video and audio streams
            probe_result = subprocess.run([
                'ffprobe',
                '-v', 'error',
                '-show_entries', 'stream=codec_type,codec_name',
                '-of', 'json',
                output_path
            ], capture_output=True, text=True)

            if 'audio' not in probe_result.stdout:
                logger.error(f"Output clip {output_path} has no audio track")
                continue

            logger.info(f"Successfully created clip: {output_path}")
            output_paths.append(output_path)

        return output_paths
    except Exception as e:
        logger.error(f"Error in clip_video_from_text: {str(e)}")
        raise


def trim_video(input_path: str, output_path: str, start_time: float, end_time: float) -> str:
    """Trim video to specified start and end times"""
    video = None
    trimmed = None
    try:
        video = VideoFileClip(input_path)
        trimmed = video.subclip(start_time, end_time)
        trimmed.write_videofile(output_path)
        return output_path
    except Exception as e:
        logger.error(f"Error in trim_video: {str(e)}")
        raise
    finally:
        if trimmed is not None:
            try:
                del trimmed
            except Exception as e:
                logger.error(f"Error deleting trimmed video: {str(e)}")
        if video is not None:
            try:
                del video
            except Exception as e:
                logger.error(f"Error deleting video: {str(e)}")


def adjust_speed(input_path: str, output_path: str, speed_factor: float) -> str:
    """Adjust video speed"""
    video = None
    modified = None
    try:
        video = VideoFileClip(input_path)
        modified = video.fx(VideoFileClip.speedx, speed_factor)
        modified.write_videofile(output_path)
        return output_path
    except Exception as e:
        logger.error(f"Error in adjust_speed: {str(e)}")
        raise
    finally:
        if modified is not None:
            try:
                del modified
            except Exception as e:
                logger.error(f"Error deleting modified video: {str(e)}")
        if video is not None:
            try:
                del video
            except Exception as e:
                logger.error(f"Error deleting video: {str(e)}")


def crop_video(input_path: str, output_path: str, x1: int, y1: int, x2: int, y2: int) -> str:
    """Crop video to specified rectangle"""
    video = None
    cropped = None
    try:
        video = VideoFileClip(input_path)
        cropped = video.crop(x1=x1, y1=y1, x2=x2, y2=y2)
        cropped.write_videofile(output_path)
        return output_path
    except Exception as e:
        logger.error(f"Error in crop_video: {str(e)}")
        raise
    finally:
        if cropped is not None:
            try:
                del cropped
            except Exception as e:
                logger.error(f"Error deleting cropped video: {str(e)}")
        if video is not None:
            try:
                del video
            except Exception as e:
                logger.error(f"Error deleting video: {str(e)}")


def rotate_video(input_path: str, output_path: str, angle: int) -> str:
    """Rotate video by specified angle"""
    video = None
    rotated = None
    try:
        video = VideoFileClip(input_path)
        rotated = video.rotate(angle)
        rotated.write_videofile(output_path)
        return output_path
    except Exception as e:
        logger.error(f"Error in rotate_video: {str(e)}")
        raise
    finally:
        if rotated is not None:
            try:
                del rotated
            except Exception as e:
                logger.error(f"Error deleting rotated video: {str(e)}")
        if video is not None:
            try:
                del video
            except Exception as e:
                logger.error(f"Error deleting video: {str(e)}")


def merge_videos(input_paths: List[str], output_path: str) -> str:
    """Merge multiple videos"""
    clips = []
    final_clip = None
    try:
        clips = [VideoFileClip(path) for path in input_paths]
        final_clip = concatenate_videoclips(clips)
        final_clip.write_videofile(output_path)
        return output_path
    except Exception as e:
        logger.error(f"Error in merge_videos: {str(e)}")
        raise
    finally:
        if final_clip is not None:
            try:
                del final_clip
            except Exception as e:
                logger.error(f"Error deleting final clip: {str(e)}")
        for clip in clips:
            try:
                del clip
            except Exception as e:
                logger.error(f"Error deleting clip: {str(e)}")


def segment_video_uniformly(video_path: str, min_duration: int, max_duration: int) -> List[Dict]:
    """Create segments of fixed length if transcription fails"""
    video = None
    try:
        video = VideoFileClip(video_path)
        duration = video.duration
        # fallback clip length between limits
        segment_length = max(min_duration, min(30, max_duration))

        segments = []
        start = 0
        while start < duration:
            end = min(start + segment_length, duration)
            segments.append({
                "start": start,
                "end": end,
                "text": f"Clip from {start:.2f}s to {end:.2f}s"
            })
            start = end

        return segments
    except Exception as e:
        logger.error(f"Error in segment_video_uniformly: {str(e)}")
        raise
    finally:
        if video is not None:
            try:
                del video
            except Exception as e:
                logger.error(f"Error deleting video object: {str(e)}")

#!/usr/bin/env python3
"""
Test script specifically for the new intelligent segmentation functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import group_words_into_segments, _is_engaging_content
import video_processing

def test_engaging_content_detection():
    """Test the engaging content detection logic"""
    print("🔍 Testing engaging content detection...")
    
    test_cases = [
        ("This is amazing! You won't believe what happens next!", True),
        ("Check out this incredible trick that will blow your mind!", True),
        ("Here's a simple tutorial on basic concepts.", False),
        ("What?! This is the best hack ever discovered!", True),
        ("The weather today is quite nice and pleasant.", False),
        ("Never make this mistake again! Here's the secret tip.", True),
        ("This is a regular conversation about normal topics.", False),
    ]
    
    passed = 0
    for text, expected in test_cases:
        result = _is_engaging_content(text)
        status = "✅" if result == expected else "❌"
        print(f"  {status} '{text[:40]}...' -> {result} (expected {expected})")
        if result == expected:
            passed += 1
    
    print(f"Engaging content detection: {passed}/{len(test_cases)} tests passed\n")
    return passed == len(test_cases)

def test_intelligent_segmentation():
    """Test the intelligent segmentation with realistic data"""
    print("🔍 Testing intelligent segmentation...")
    
    # Mock word-level segments that simulate a real transcript
    mock_segments = [
        {"text": "Hey", "start": 0.0, "end": 0.3},
        {"text": "everyone!", "start": 0.3, "end": 0.8},
        {"text": "This", "start": 1.0, "end": 1.2},
        {"text": "is", "start": 1.2, "end": 1.3},
        {"text": "an", "start": 1.3, "end": 1.4},
        {"text": "amazing", "start": 1.4, "end": 1.9},
        {"text": "tutorial", "start": 1.9, "end": 2.5},
        {"text": "that", "start": 2.5, "end": 2.7},
        {"text": "will", "start": 2.7, "end": 2.9},
        {"text": "blow", "start": 2.9, "end": 3.2},
        {"text": "your", "start": 3.2, "end": 3.4},
        {"text": "mind!", "start": 3.4, "end": 4.0},
        {"text": "First,", "start": 5.0, "end": 5.4},
        {"text": "let", "start": 5.4, "end": 5.6},
        {"text": "me", "start": 5.6, "end": 5.7},
        {"text": "show", "start": 5.7, "end": 6.0},
        {"text": "you", "start": 6.0, "end": 6.2},
        {"text": "this", "start": 6.2, "end": 6.5},
        {"text": "incredible", "start": 6.5, "end": 7.2},
        {"text": "trick.", "start": 7.2, "end": 7.8},
        {"text": "You", "start": 8.5, "end": 8.7},
        {"text": "simply", "start": 8.7, "end": 9.2},
        {"text": "need", "start": 9.2, "end": 9.5},
        {"text": "to", "start": 9.5, "end": 9.6},
        {"text": "follow", "start": 9.6, "end": 10.1},
        {"text": "these", "start": 10.1, "end": 10.4},
        {"text": "steps", "start": 10.4, "end": 10.9},
        {"text": "carefully.", "start": 10.9, "end": 11.8},
        {"text": "The", "start": 12.5, "end": 12.7},
        {"text": "secret", "start": 12.7, "end": 13.2},
        {"text": "is", "start": 13.2, "end": 13.4},
        {"text": "in", "start": 13.4, "end": 13.5},
        {"text": "the", "start": 13.5, "end": 13.7},
        {"text": "timing!", "start": 13.7, "end": 14.5},
        {"text": "Most", "start": 15.0, "end": 15.3},
        {"text": "people", "start": 15.3, "end": 15.8},
        {"text": "make", "start": 15.8, "end": 16.1},
        {"text": "this", "start": 16.1, "end": 16.4},
        {"text": "mistake", "start": 16.4, "end": 17.0},
        {"text": "and", "start": 17.0, "end": 17.2},
        {"text": "fail.", "start": 17.2, "end": 17.8},
    ]
    
    try:
        # Test with different duration parameters
        test_cases = [
            (10.0, 30.0, "Standard duration range"),
            (5.0, 20.0, "Shorter clips preferred"),
            (15.0, 45.0, "Longer clips allowed"),
        ]
        
        for min_dur, max_dur, description in test_cases:
            print(f"  Testing {description} (min: {min_dur}s, max: {max_dur}s)")
            
            segments = group_words_into_segments(mock_segments, min_dur, max_dur)
            
            if not segments:
                print(f"    ❌ No segments created")
                continue
            
            print(f"    ✅ Created {len(segments)} segments")
            
            # Validate segment properties
            valid_segments = 0
            for i, segment in enumerate(segments):
                duration = segment['end'] - segment['start']
                is_valid = min_dur <= duration <= max_dur
                
                if is_valid:
                    valid_segments += 1
                
                print(f"      Segment {i+1}: {duration:.1f}s - {'✅' if is_valid else '❌'}")
                print(f"        Text: '{segment['text'][:50]}...'")
            
            print(f"    Valid segments: {valid_segments}/{len(segments)}\n")
        
        return True
        
    except Exception as e:
        print(f"❌ Intelligent segmentation test error: {str(e)}")
        return False

def test_video_processing_integration():
    """Test integration with video processing module"""
    print("🔍 Testing video processing integration...")
    
    try:
        # Test that the new functions are properly integrated
        mock_transcript = "This is a test. It has multiple sentences! Some are engaging."
        mock_timestamps = [
            {"word": "This", "start": 0.0, "end": 0.5},
            {"word": "is", "start": 0.5, "end": 0.7},
            {"word": "a", "start": 0.7, "end": 0.8},
            {"word": "test.", "start": 0.8, "end": 1.5},
            {"word": "It", "start": 2.0, "end": 2.2},
            {"word": "has", "start": 2.2, "end": 2.5},
            {"word": "multiple", "start": 2.5, "end": 3.2},
            {"word": "sentences!", "start": 3.2, "end": 4.0},
            {"word": "Some", "start": 4.5, "end": 4.8},
            {"word": "are", "start": 4.8, "end": 5.0},
            {"word": "engaging.", "start": 5.0, "end": 5.8},
        ]
        
        # Test without AI (basic segmentation)
        segments_basic = video_processing.segment_transcript(
            mock_transcript, mock_timestamps, 
            min_duration=2.0, max_duration=8.0, 
            refine_with_ai=False
        )
        
        if segments_basic and len(segments_basic) > 0:
            print(f"  ✅ Basic segmentation: {len(segments_basic)} segments")
            for i, seg in enumerate(segments_basic):
                duration = seg['end'] - seg['start']
                print(f"    Segment {i+1}: {duration:.1f}s - '{seg['text'][:40]}...'")
        else:
            print("  ❌ Basic segmentation failed")
            return False
        
        print("  ✅ Video processing integration successful\n")
        return True
        
    except Exception as e:
        print(f"  ❌ Video processing integration error: {str(e)}\n")
        return False

def main():
    """Run all intelligent segmentation tests"""
    print("🚀 Testing SmartClips Intelligent Segmentation\n")
    
    tests = [
        test_engaging_content_detection,
        test_intelligent_segmentation,
        test_video_processing_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"📊 Intelligent Segmentation Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All intelligent segmentation tests passed!")
        print("✨ The AI clipper now uses max_duration as a limit and creates optimal clips!")
    else:
        print("⚠️  Some intelligent segmentation tests failed.")
    
    return passed == total

if __name__ == "__main__":
    main()

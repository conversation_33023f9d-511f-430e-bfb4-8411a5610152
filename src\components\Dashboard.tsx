import React, { ReactNode } from "react";
import {
  SidebarProvider,
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarFooter,
  SidebarSeparator,
} from "@/components/ui/sidebar";
import { Link } from "react-router-dom";
import {
  VideoIcon,
  Film,
  Settings,
  LogOut,
  LayoutDashboard,
  Cloud,
  Calendar,
  Share2,
  Image,
  Scissors,
  BookOpen,
  CreditCard,
  User,
  Users,
  BarChart,
  Play,
  Edit,
  HelpCircle,
  Home,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import ThemeToggle from "./ThemeToggle";
import { useAuth } from "@/context/AuthContext";
import { Badge } from "@/components/ui/badge";

interface DashboardProps {
  children: ReactNode;
}

const Dashboard: React.FC<DashboardProps> = ({ children }) => {
  const { isAuthenticated, logout, user, isAdmin } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  return (
    <SidebarProvider defaultOpen={true}>
      <div className="flex w-full min-h-svh">
        {/* {isAuthenticated && ( */}
        <Sidebar>
          <SidebarHeader>
            <div className="flex items-center gap-2 px-4 py-2">
              <VideoIcon className="h-6 w-6 text-primary" />
              <span className="font-bold text-xl gradient-text">
                SmartClips
              </span>
            </div>
          </SidebarHeader>

          <SidebarContent>
            <SidebarGroup>
              <SidebarGroupLabel>Navigation</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  <SidebarMenuItem>
                    <SidebarMenuButton asChild tooltip="Home">
                      <Link to="/">
                        <Home className="mr-2" />
                        <span>Home</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>

                  {isAuthenticated && (
                    <>
                      <SidebarMenuItem>
                        <SidebarMenuButton asChild tooltip="Calendar">
                          <Link to="/calendar">
                            <Calendar className="mr-2" />
                            <span>Calendar</span>
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>

                      <SidebarMenuItem>
                        <SidebarMenuButton asChild tooltip="Analytics">
                          <Link to="/analytics">
                            <BarChart className="mr-2" />
                            <span>Analytics</span>
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>

                      <SidebarMenuItem>
                        <SidebarMenuButton asChild tooltip="Credits">
                          <Link to="/credits">
                            <CreditCard className="mr-2" />
                            <span>Credits</span>
                            <Badge className="ml-auto bg-primary">
                              {user?.credits || 0}
                            </Badge>
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    </>
                  )}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>

            <SidebarSeparator />

            <SidebarGroup>
              <SidebarGroupLabel>Create</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  <SidebarMenuItem>
                    <SidebarMenuButton asChild tooltip="Smart Clipper">
                      <Link to="/smart-clipper">
                        <Scissors className="mr-2" />
                        <span>Smart Clipper</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>

                  {isAuthenticated && (
                    <>
                      <SidebarMenuItem>
                        <SidebarMenuButton asChild tooltip="Clip Results">
                          <Link to="/clip-results">
                            <Play className="mr-2" />
                            <span>My Clips</span>
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    </>
                  )}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>

            <SidebarSeparator />

            {isAdmin && (
              <>
                <SidebarGroup>
                  <SidebarGroupLabel>Admin</SidebarGroupLabel>
                  <SidebarGroupContent>
                    <SidebarMenu>
                      <SidebarMenuItem>
                        <SidebarMenuButton asChild tooltip="Users">
                          <Link to="/admin/users">
                            <Users className="mr-2" />
                            <span>Users</span>
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>

                      <SidebarMenuItem>
                        <SidebarMenuButton asChild tooltip="Analytics">
                          <Link to="/admin/analytics">
                            <BarChart className="mr-2" />
                            <span>Analytics</span>
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    </SidebarMenu>
                  </SidebarGroupContent>
                </SidebarGroup>

                <SidebarSeparator />
              </>
            )}

            {isAuthenticated && (
              <SidebarGroup>
                <SidebarGroupLabel>Integrations</SidebarGroupLabel>
                <SidebarGroupContent>
                  <SidebarMenu>
                    <SidebarMenuItem>
                      <SidebarMenuButton asChild tooltip="Social Media">
                        <Link to="/social-integration">
                          <Share2 className="mr-2" />
                          <span>Social Media</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>

                    <SidebarMenuItem>
                      <SidebarMenuButton asChild tooltip="Cloudinary">
                        <Link to="/cloudinary">
                          <Cloud className="mr-2" />
                          <span>Cloudinary</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  </SidebarMenu>
                </SidebarGroupContent>
              </SidebarGroup>
            )}

            <SidebarSeparator />

            <SidebarGroup>
              <SidebarGroupLabel>Support</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {isAuthenticated && (
                    <SidebarMenuItem>
                      <SidebarMenuButton asChild tooltip="Support">
                        <Link to="/support">
                          <HelpCircle className="mr-2" />
                          <span>Support</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  )}

                  {!isAuthenticated && (
                    <>
                      <SidebarMenuItem>
                        <SidebarMenuButton asChild tooltip="Login">
                          <Link to="/login">
                            <User className="mr-2" />
                            <span>Login</span>
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                      <SidebarMenuItem>
                        <SidebarMenuButton asChild tooltip="Register">
                          <Link to="/register">
                            <User className="mr-2" />
                            <span>Register</span>
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    </>
                  )}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </SidebarContent>

          <SidebarFooter>
            <div className="p-2">
              <ThemeToggle />
            </div>
            {isAuthenticated && (
              <SidebarGroup>
                <SidebarGroupContent>
                  <SidebarMenu>
                    <SidebarMenuItem>
                      <SidebarMenuButton asChild tooltip="Profile">
                        <Link to="/profile">
                          <User className="mr-2" />
                          <span>{user?.username || "Profile"}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>

                    <SidebarMenuItem>
                      <SidebarMenuButton asChild tooltip="Settings">
                        <Link to="/settings">
                          <Settings className="mr-2" />
                          <span>Settings</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>

                    <SidebarMenuItem>
                      <SidebarMenuButton asChild>
                        <Button
                          variant="ghost"
                          className="w-full justify-start"
                          onClick={handleLogout}
                        >
                          <LogOut className="mr-2 h-4 w-4" />
                          <span>Log out</span>
                        </Button>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  </SidebarMenu>
                </SidebarGroupContent>
              </SidebarGroup>
            )}
          </SidebarFooter>
        </Sidebar>
        {/* )} */}

        <div className="flex-1 flex flex-col">
          <div className="p-8">{children}</div>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default Dashboard;

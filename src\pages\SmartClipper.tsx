import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { WithChildren } from "@/components/types";
import VideoUploader<PERSON>ith<PERSON><PERSON> from "@/components/VideoUploaderWithAPI";
import URLProcessor from "@/components/URLProcessor";
import { toast } from "@/hooks/use-toast";
import DashboardLayout from "@/components/Dashboard";
import { useForm } from "react-hook-form";
import { supabase } from "@/lib/supabase";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import { deductSecondsFromUser } from "@/lib/user";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Upload,
  Link,
  Settings,
  Play,
  Download,
  Scissors,
  Clock,
  Target,
  Zap,
  Sparkles,
} from "lucide-react";
import { Switch } from "@/components/ui/switch";

// Form schema with enhanced controls
const clipFormSchema = z
  .object({
    // prompt: z.string().min(10, "Prompt must be at least 10 characters long"),
    maxClips: z.number().min(1).max(10).default(3),
    minDuration: z.number().min(5).max(60).default(15),
    maxDuration: z.number().min(10).max(300).default(60),
    platform: z.string().default("youtube"),
    analyze_virality: z.boolean().default(false),
  })
  .refine((data) => data.maxDuration >= data.minDuration, {
    message: "Max duration must be greater than or equal to min duration.",
    path: ["maxDuration"],
  });

type ClipFormValues = z.infer<typeof clipFormSchema>;

interface VideoClip {
  id: string;
  url: string;
  title: string;
  duration: string;
  vitalityScore?: number;
}

interface PlatformPreset {
  name: string;
  minDuration: number;
  maxDuration: number;
  icon: string;
}

const platformPresets: Record<string, PlatformPreset> = {
  youtube: {
    name: "YouTube Shorts",
    minDuration: 15,
    maxDuration: 60,
    icon: "🎥",
  },
  tiktok: { name: "TikTok", minDuration: 15, maxDuration: 60, icon: "🎵" },
  instagram: {
    name: "Instagram Reels",
    minDuration: 15,
    maxDuration: 90,
    icon: "📸",
  },
  twitter: { name: "Twitter/X", minDuration: 5, maxDuration: 140, icon: "🐦" },
  facebook: { name: "Facebook", minDuration: 10, maxDuration: 240, icon: "👥" },
  custom: { name: "Custom", minDuration: 5, maxDuration: 300, icon: "⚙️" },
};

// NEW: Define a type for the virality analysis results
interface ViralityAnalysis {
  virality_score?: number;
  analysis?: string;
  catchy_title?: string;
  [key: string]: any;
}

const Layout = ({ children }: WithChildren) => {
  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-2">Smart Clipper</h1>
      <p className="text-foreground/70 mb-8">
        Upload a video and use AI prompts to automatically clip it into engaging
        segments.
      </p>
      {children}
    </div>
  );
};

const SmartClipper = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<"upload" | "url">("upload");
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [videoUrl, setVideoUrl] = useState<string>("");
  const [cloudinaryUrl, setCloudinaryUrl] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [clips, setClips] = useState<VideoClip[]>([]);
  const [selectedPreset, setSelectedPreset] = useState<string>("youtube");
  const [viralityAnalysis, setViralityAnalysis] =
    useState<ViralityAnalysis | null>(null);
  const [isInstantProcessing, setIsInstantProcessing] = useState(false);

  const handleGetClips = async () => {
    if (!videoUrl) {
      setError("Please enter a video URL.");
      return;
    }
    setError(null);
    setIsProcessing(true);

    try {
      const payload = {
        video_url: videoUrl, // Use state for the URL
        options: {
          add_subtitles: true,
          add_emojis: addEmojis, // Use state for the emoji toggle
          create_short_form: true,
          platforms: ["tiktok"],
          subtitle_style: "viral_style",
          max_short_clips: 5,
        },
      };

      const formData = new FormData();
      formData.append("request", JSON.stringify(payload));

      const response = await fetch("http://localhost:8000/advanced-process", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Processing failed");
      }

      const data = await response.json();

      if (!data.success || !data.clips) {
        throw new Error(data.message || "Failed to retrieve processed clips.");
      }

      // Success! You can now handle the response data.
      // For example, redirect to a results page or show a success message.
      console.log("Successfully processed:", data);
      alert("Clips generated successfully!");
    } catch (err) {
      console.error(err);
      setError(err.message);
    } finally {
      setIsProcessing(false);
    }
  };

  const form = useForm<ClipFormValues>({
    resolver: zodResolver(clipFormSchema),
    defaultValues: {
      prompt: "",
      maxClips: 3,
      minDuration: 15,
      maxDuration: 60,
      platform: "youtube",
      analyze_virality: false,
    },
  });

  useEffect(() => {
    try {
      const userProfileString = localStorage.getItem("userProfile");

      if (!userProfileString) {
        toast({
          title: "User Profile Not Found",
          description: "Please log in to use the Smart Clipper.",
          variant: "destructive",
        });
        navigate("/credits");
        return;
      }

      const userProfile = JSON.parse(userProfileString);

      console.log("User Profile:", userProfile);
      if (
        userProfile &&
        typeof userProfile.credits === "number" &&
        userProfile.credits <= 0
      ) {
        toast({
          title: "Out of Credits",
          description: "Please top up your credits to continue creating clips.",
          variant: "destructive",
        });
        navigate("/credits");
      }
    } catch (error) {
      console.error("Failed to check user credits:", error);
      toast({
        title: "Error Verifying Credits",
        description:
          "There was a problem checking your account status. Redirecting.",
        variant: "destructive",
      });
      navigate("/credits");
    }
  }, [navigate]);

  useEffect(() => {
    let progressInterval: NodeJS.Timeout | null = null;

    if (isProcessing) {
      setProgress(0); // Reset progress to 0 when processing starts
      // Simulate progress moving from 0% to 90% over ~45 seconds
      progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) {
            if (progressInterval) clearInterval(progressInterval);
            return 90; // Stop at 90%
          }
          return prev + 2; // Increment by 2 every second
        });
      }, 1000);
    } else {
      // If processing stops for any reason (complete or error), clear the interval.
      setProgress(0);
    }

    // Cleanup function to clear interval if the component unmounts
    return () => {
      if (progressInterval) {
        clearInterval(progressInterval);
      }
    };
  }, [isProcessing]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${String(minutes).padStart(2, "0")}:${String(
      remainingSeconds
    ).padStart(2, "0")}`;
  };

  // Update form values when preset changes
  const handlePresetChange = (preset: string) => {
    setSelectedPreset(preset);
    const presetData = platformPresets[preset];
    form.setValue("platform", preset);
    form.setValue("minDuration", presetData.minDuration);
    form.setValue("maxDuration", presetData.maxDuration);
  };
  const hasVideoSource = videoFile || videoUrl || cloudinaryUrl;

  const handleUpload = async (file: File) => {
    try {
      setVideoFile(file);
      setIsProcessing(true);

      // Upload to Cloudinary
      const formData = new FormData();
      formData.append("file", file);
      formData.append("upload_preset", "smartclips");

      const uploadResponse = await fetch(
        `https://api.cloudinary.com/v1_1/dohzhixsn/video/upload`,
        {
          method: "POST",
          body: formData,
        }
      );

      console.log("Upload response:", uploadResponse);
      if (!uploadResponse.ok) throw new Error("Upload to Cloudinary failed");

      const uploadResult = await uploadResponse.json();
      setCloudinaryUrl(uploadResult.secure_url);

      toast({
        title: "Video uploaded",
        description:
          "Your video has been uploaded successfully. Configure your clip settings below.",
      });
    } catch (error) {
      toast({
        title: "Upload failed",
        description:
          error instanceof Error ? error.message : "Failed to upload video",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleURLProcess = async (result: any) => {
    try {
      setVideoUrl(result?.url || "");
      setCloudinaryUrl(result?.url || "");

      // Generate mock clips with vitality scores for demo
      const mockClips =
        result?.video_urls?.map((url: string, index: number) => ({
          id: `clip-${index}`,
          url,
          title: `Clip ${index + 1}`,
          duration: `${Math.floor(Math.random() * 30 + 15)}s`,
          vitalityScore: Math.floor(Math.random() * 40 + 60), // 60-100 range
        })) || [];

      setClips(mockClips);

      toast({
        title: "URL processed successfully",
        description: `Generated clips from the video URL.`,
      });
    } catch (error) {
      toast({
        title: "URL processing failed",
        description:
          error instanceof Error ? error.message : "Failed to process URL",
        variant: "destructive",
      });
    }
  };

  const onSubmit = async (values: ClipFormValues) => {
    if (!cloudinaryUrl && !videoUrl) {
      toast({
        title: "Error",
        description: "Please upload a video or provide a URL first",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    try {
      const response = await fetch("http://localhost:8000/process-url", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
        body: JSON.stringify({
          url: cloudinaryUrl || videoUrl,
          prompt: values.prompt,
          max_clips: values.maxClips,
          min_duration: values.minDuration,
          max_duration: values.maxDuration,
          platform: values.platform,
          analyze_virality: values.analyze_virality,
          subscription: JSON.parse(localStorage.getItem("userProfile"))
            ?.subscription,
        }),
      });

      if (!response.ok) throw new Error("Failed to process video");

      const data = await response.json();

      console.log(`Processing videoUrl:`, videoUrl);

      console.log(`Processing response:`, data);

      const { video_urls, segments, ...viralityData } = data;

      if (!Array.isArray(data.clips)) {
        throw new Error(
          "Invalid response format: 'clips' is missing or not an array."
        );
      }
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (!session?.user?.id) {
        throw new Error("User not authenticated. Please log in again.");
      }
      const userId = session.user.id;

      const totalDurationInSeconds = Math.ceil(
        data.clips.reduce((total: number, clip: any) => {
          // Ensure start and end times are valid numbers
          const startTime = Number(clip.start_time) || 0;
          const endTime = Number(clip.end_time) || 0;
          const duration = endTime - startTime;
          return total + (duration > 0 ? duration : 0);
        }, 0)
      );

      if (totalDurationInSeconds <= 0) {
        throw new Error(
          "Could not calculate a valid duration for the generated clips."
        );
      }

      // 2. Attempt to deduct the seconds. This is the "pre-authorization".
      // const deductionSuccessful = await deductSecondsFromUser(
      //   userId,
      //   totalDurationInSeconds
      // );

      // // 3. If deduction fails (e.g., insufficient funds), stop immediately.
      // // The helper function will have already shown the appropriate toast.
      // if (!deductionSuccessful) {
      //   throw new Error("Credit deduction failed. Aborting process.");
      // }

      console.log(`Processing result:`, data);

      const insertData = data?.clips?.map((clip) => ({
        user_id: session?.user.id,
        url: clip.url,
        text: clip.text,
        start_time: clip.start_time,
        end_time: clip.end_time,
        score: clip.virality_analysis?.score,
        feedback: clip.virality_analysis?.feedback,
        platform: clip.platform,
      }));
      console.log(`Processing insertData:`, insertData);

      const { error } = await supabase
        .from("myclip")
        .insert(insertData)
        .select();

      if (error) throw error;
      setProgress(100);
      toast({
        title: "Processing complete",
        description: `Generated clips. ${
          viralityData.analysis ? "Virality analysis also complete." : ""
        }`,
      });
      navigate("/clip-results");
    } catch (error) {
      toast({
        title: "Processing failed",
        description:
          error instanceof Error ? error.message : "Failed to process video",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const resetForm = () => {
    setVideoFile(null);
    setVideoUrl("");
    setCloudinaryUrl(null);
    setClips([]);
    setViralityAnalysis(null);
    form.reset();
    setActiveTab("upload");
  };

  const handleInstantProcess = async () => {
    if (!cloudinaryUrl) {
      toast({
        title: "Error",
        description: "Please upload a video first.",
        variant: "destructive",
      });
      return;
    }

    setIsInstantProcessing(true);
    setProgress(0);

    try {
      const dummyPayload = {
        video_url: "https://x.com/stardusthome/status/1937548770184691779",
        options: {
          add_subtitles: true,
          add_emojis: true,
          create_short_form: true,
          platforms: ["tiktok"],
          subtitle_style: "viral_style",
          max_short_clips: 5,
        },
      };

      const formData = new FormData();

      formData.append("request", JSON.stringify(dummyPayload));

      const response = await fetch("http://localhost:8000/advanced-process", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Instant processing failed");
      }

      const data = await response.json();

      if (!data.success || !data.clips) {
        throw new Error(data.message || "Failed to retrieve processed clips.");
      }

      console.log("Instant Process successful:", data.clips);
      setProgress(100);
      toast({
        title: "Instant Enhancement Complete",
        description: `Successfully created ${data.clips.length} enhanced clips.`,
      });
    } catch (error) {
      toast({
        title: "Instant Processing Failed",
        description:
          error instanceof Error ? error.message : "An unknown error occurred.",
        variant: "destructive",
      });
    } finally {
      setIsInstantProcessing(false);
      setProgress(0);
    }
  };

  return (
    <DashboardLayout>
      <Layout>
        {!hasVideoSource ? (
          <div className="space-y-6 px-4 sm:px-6 lg:px-8">
            {/* Input Method Tabs */}
            <Tabs
              value={activeTab}
              onValueChange={(value) => setActiveTab(value as "upload" | "url")}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger
                  value="upload"
                  className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm"
                >
                  <Upload className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline">Upload File</span>
                  <span className="sm:hidden">Upload</span>
                </TabsTrigger>
                <TabsTrigger
                  value="url"
                  className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm"
                >
                  <Link className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline">Process URL</span>
                  <span className="sm:hidden">URL</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="upload" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Upload className="h-5 w-5" />
                      Upload Video File
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <VideoUploaderWithAPI
                      description="Upload a video file to get started. We support MP4, MOV, and AVI formats up to 10 minutes."
                      onUploadComplete={handleUpload}
                      uploadToServer={false}
                      acceptedFormats="video/mp4,video/quicktime,video/avi"
                      minDuration={10}
                      maxDuration={600}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="url" className="mt-6">
                <URLProcessor
                  onProcessComplete={handleURLProcess}
                  onError={(error) => {
                    toast({
                      title: "URL Processing Failed",
                      description: error,
                      variant: "destructive",
                    });
                  }}
                />
              </TabsContent>
            </Tabs>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Video Source Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <Play className="h-5 w-5" />
                    Video Ready for Processing
                  </span>
                  <Badge variant="outline">
                    {videoFile ? "Uploaded File" : "URL Source"}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  {videoFile
                    ? `File: ${videoFile.name}`
                    : `URL: ${videoUrl || "External source"}`}
                </p>
              </CardContent>
            </Card>

            {/* Configuration Form */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Clip Configuration
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-6"
                  >
                    {/* Platform Presets */}
                    <div className="space-y-3">
                      <FormLabel>Platform Optimization</FormLabel>
                      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-3">
                        {Object.entries(platformPresets).map(
                          ([key, preset]) => (
                            <Button
                              key={key}
                              type="button"
                              variant={
                                selectedPreset === key ? "default" : "outline"
                              }
                              className="h-auto p-2 sm:p-3 flex flex-col items-center gap-1 sm:gap-2"
                              onClick={() => handlePresetChange(key)}
                            >
                              <span className="text-base sm:text-lg">
                                {preset.icon}
                              </span>
                              <div className="text-center">
                                <div className="font-medium text-xs">
                                  {preset.name}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {preset.minDuration}-{preset.maxDuration}s
                                </div>
                              </div>
                            </Button>
                          )
                        )}
                      </div>
                    </div>

                    <FormField
                      control={form.control}
                      name="analyze_virality"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base flex items-center gap-2">
                              <Sparkles className="h-4 w-4 text-yellow-500" />
                              AI Virality Analysis
                            </FormLabel>
                            <FormDescription>
                              Analyze transcript for virality potential,
                              engagement hooks, and title suggestions.
                            </FormDescription>
                          </div>
                          {/* <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl> */}
                        </FormItem>
                      )}
                    />

                    {/* Duration Controls */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      <FormField
                        control={form.control}
                        name="maxClips"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-2 text-sm">
                              <Scissors className="h-4 w-4" />
                              Max Clips
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="1"
                                max="10"
                                {...field}
                                onChange={(e) =>
                                  field.onChange(parseInt(e.target.value))
                                }
                                className="text-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="minDuration"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-2">
                              <Clock className="h-4 w-4" />
                              Min Duration (s)
                            </FormLabel>
                            <FormControl>
                              <div className="space-y-2">
                                <Slider
                                  value={[field.value]}
                                  onValueChange={(value) => {
                                    const newMin = value[0];
                                    const currentMax =
                                      form.getValues("maxDuration");
                                    if (newMin > currentMax) {
                                      form.setValue("maxDuration", newMin); // Adjust max if min exceeds it
                                    }
                                    field.onChange(newMin);
                                  }}
                                  min={5}
                                  max={60}
                                  step={5}
                                  className="w-full"
                                />
                                <div className="text-center text-sm text-muted-foreground">
                                  {field.value}s
                                </div>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="maxDuration"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-2">
                              <Target className="h-4 w-4" />
                              Max Duration (s)
                            </FormLabel>
                            <FormControl>
                              <div className="space-y-2">
                                <Slider
                                  value={[field.value]}
                                  onValueChange={(value) => {
                                    const newMax = value[0];
                                    const currentMin =
                                      form.getValues("minDuration");
                                    if (newMax < currentMin) {
                                      form.setValue("minDuration", newMax);
                                    }
                                    field.onChange(newMax);
                                  }}
                                  min={10}
                                  max={300}
                                  step={10}
                                  className="w-full"
                                />
                                <div className="text-center text-sm text-muted-foreground">
                                  {field.value}s
                                </div>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Submit Button */}
                    <div className="flex gap-3">
                      <Button
                        type="submit"
                        disabled={isProcessing}
                        className="flex-1"
                        size="lg"
                      >
                        {isProcessing ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                            Processing...
                          </>
                        ) : (
                          <>
                            <Scissors className="mr-2 h-4 w-4" />
                            Generate Clips
                          </>
                        )}
                      </Button>
                      {/* <Button
                        type="submit"
                        disabled={isProcessing || isInstantProcessing}
                        className="flex-1"
                        size="lg"
                      >
                        {isProcessing ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                            Processing... {progress}%
                          </>
                        ) : (
                          <>
                            <Scissors className="mr-2 h-4 w-4" />
                            Generate Clips
                          </>
                        )}
                      </Button> */}

                      <Button
                        type="button"
                        variant="outline"
                        onClick={resetForm}
                        disabled={isProcessing || isInstantProcessing}
                        size="lg"
                      >
                        Reset
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>

            {/* Generated Clips Results */}
            {clips.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Play className="h-5 w-5" />
                      Generated Clips ({clips.length})
                    </span>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-1" />
                        Download All
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                    {clips.map((clip) => (
                      <div
                        key={clip.id}
                        className="border rounded-lg p-3 sm:p-4 space-y-3"
                      >
                        <video
                          src={clip.url}
                          controls
                          className="w-full rounded-lg aspect-video object-cover"
                          poster="/api/placeholder/300/200"
                        />

                        <div className="space-y-2">
                          <div className="flex items-start justify-between gap-2">
                            <h4 className="font-medium text-sm sm:text-base line-clamp-2 flex-1">
                              {clip.title}
                            </h4>
                            {clip.vitalityScore && (
                              <Badge
                                variant={
                                  clip.vitalityScore >= 80
                                    ? "default"
                                    : clip.vitalityScore >= 60
                                    ? "secondary"
                                    : "outline"
                                }
                                className="text-xs flex-shrink-0"
                              >
                                {clip.vitalityScore}%
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Processing Tips */}
                  <div className="mt-6 p-4 bg-muted rounded-lg">
                    <h4 className="font-medium mb-2">
                      💡 Pro Tips for Better Results
                    </h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>
                        • Use specific prompts like "Find moments with high
                        energy" or "Extract educational content"
                      </li>
                      <li>
                        • Shorter clips (15-30s) typically perform better on
                        social media
                      </li>
                      <li>
                        • Higher vitality scores indicate more engaging content
                      </li>
                      <li>
                        • Try different platform presets to optimize for your
                        target audience
                      </li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </Layout>
    </DashboardLayout>
  );
};

export default SmartClipper;
function setError(arg0: string) {
  throw new Error("Function not implemented.");
}

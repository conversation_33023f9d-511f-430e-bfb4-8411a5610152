import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Heart,
  MessageCircle,
  Share,
  Download,
  MoreVertical,
  Edit
} from 'lucide-react';
import { UserClip } from '@/services/userService';

interface TikTokFeedProps {
  clips: UserClip[];
  onEdit?: (clipId: string) => void;
  onShare?: (clipId: string) => void;
  onDownload?: (clipId: string) => void;
}

interface VideoPlayerProps {
  clip: UserClip;
  isActive: boolean;
  isMuted: boolean;
  onToggleMute: () => void;
  onEdit?: (clipId: string) => void;
  onShare?: (clipId: string) => void;
  onDownload?: (clipId: string) => void;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  clip,
  isActive,
  isMuted,
  onToggleMute,
  onEdit,
  onShare,
  onDownload
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showControls, setShowControls] = useState(false);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    if (isActive) {
      video.play().then(() => {
        setIsPlaying(true);
      }).catch(console.error);
    } else {
      video.pause();
      setIsPlaying(false);
    }
  }, [isActive]);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const updateProgress = () => {
      const progress = (video.currentTime / video.duration) * 100;
      setProgress(progress);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setProgress(0);
    };

    video.addEventListener('timeupdate', updateProgress);
    video.addEventListener('ended', handleEnded);

    return () => {
      video.removeEventListener('timeupdate', updateProgress);
      video.removeEventListener('ended', handleEnded);
    };
  }, []);

  const togglePlayPause = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
      setIsPlaying(false);
    } else {
      video.play();
      setIsPlaying(true);
    }
  };

  const getVitalityColor = (score: number) => {
    if (score > 80) return 'bg-green-500';
    if (score > 50) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div
      className="relative bg-black rounded-2xl overflow-hidden aspect-[9/16] max-h-[600px] md:max-h-[700px] group cursor-pointer touch-active"
      onMouseEnter={() => setShowControls(true)}
      onMouseLeave={() => setShowControls(false)}
      onTouchStart={() => setShowControls(true)}
      onTouchEnd={() => setTimeout(() => setShowControls(false), 3000)}
      onClick={togglePlayPause}
    >
      {/* Video */}
      <video
        ref={videoRef}
        src={clip.url}
        className="w-full h-full object-cover"
        poster={clip.thumbnail}
        muted={isMuted}
        playsInline
        loop
      />

      {/* Progress bar */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/20">
        <div 
          className="h-full bg-white transition-all duration-100"
          style={{ width: `${progress}%` }}
        />
      </div>

      {/* Play/Pause overlay */}
      {showControls && (
        <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
          <Button
            size="lg"
            className="bg-black/50 hover:bg-black/70 text-white border-none rounded-full"
            onClick={(e) => {
              e.stopPropagation();
              togglePlayPause();
            }}
          >
            {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
          </Button>
        </div>
      )}

      {/* Top overlay - Status and Platform */}
      <div className="absolute top-4 left-4 right-4 flex justify-between items-start">
        <div className="flex gap-2">
          <Badge variant="secondary" className="text-xs bg-black/50 text-white border-none">
            {clip.platform}
          </Badge>
          <Badge 
            className={`text-xs text-white border-none ${getVitalityColor(clip.vitalityScore)}`}
          >
            {clip.vitalityScore}% viral
          </Badge>
        </div>
        <Badge variant="outline" className="text-xs bg-black/50 text-white border-none">
          {clip.duration}
        </Badge>
      </div>

      {/* Right side actions */}
      <div className="absolute right-2 md:right-4 bottom-16 md:bottom-20 flex flex-col gap-3 md:gap-4">
        <Button
          size="icon"
          className="bg-black/50 hover:bg-black/70 text-white border-none rounded-full touch-target w-10 h-10 md:w-12 md:h-12"
          onClick={(e) => {
            e.stopPropagation();
            onToggleMute();
          }}
        >
          {isMuted ? <VolumeX className="h-4 w-4 md:h-5 md:w-5" /> : <Volume2 className="h-4 w-4 md:h-5 md:w-5" />}
        </Button>

        <div className="flex flex-col items-center gap-1">
          <Button
            size="icon"
            className="bg-black/50 hover:bg-black/70 text-white border-none rounded-full touch-target w-10 h-10 md:w-12 md:h-12"
            onClick={(e) => {
              e.stopPropagation();
              // Handle like action
            }}
          >
            <Heart className="h-4 w-4 md:h-5 md:w-5" />
          </Button>
          <span className="text-white text-xs md:text-sm">{clip.likes > 999 ? `${(clip.likes/1000).toFixed(1)}k` : clip.likes}</span>
        </div>

        <div className="flex flex-col items-center gap-1">
          <Button
            size="icon"
            className="bg-black/50 hover:bg-black/70 text-white border-none rounded-full touch-target w-10 h-10 md:w-12 md:h-12"
            onClick={(e) => {
              e.stopPropagation();
              // Handle comment action
            }}
          >
            <MessageCircle className="h-4 w-4 md:h-5 md:w-5" />
          </Button>
          <span className="text-white text-xs md:text-sm">{clip.comments}</span>
        </div>

        <Button
          size="icon"
          className="bg-black/50 hover:bg-black/70 text-white border-none rounded-full touch-target w-10 h-10 md:w-12 md:h-12"
          onClick={(e) => {
            e.stopPropagation();
            onShare?.(clip.id);
          }}
        >
          <Share className="h-4 w-4 md:h-5 md:w-5" />
        </Button>

        <Button
          size="icon"
          className="bg-black/50 hover:bg-black/70 text-white border-none rounded-full touch-target w-10 h-10 md:w-12 md:h-12"
          onClick={(e) => {
            e.stopPropagation();
            onDownload?.(clip.id);
          }}
        >
          <Download className="h-4 w-4 md:h-5 md:w-5" />
        </Button>

        <Button
          size="icon"
          className="bg-black/50 hover:bg-black/70 text-white border-none rounded-full touch-target w-10 h-10 md:w-12 md:h-12"
          onClick={(e) => {
            e.stopPropagation();
            onEdit?.(clip.id);
          }}
        >
          <Edit className="h-4 w-4 md:h-5 md:w-5" />
        </Button>
      </div>

      {/* Bottom overlay - Title and info */}
      <div className="absolute bottom-4 left-2 md:left-4 right-16 md:right-20">
        <h3 className="text-white font-medium text-xs md:text-sm mb-1 md:mb-2 line-clamp-2">
          {clip.title}
        </h3>
        <div className="flex items-center gap-2 md:gap-4 text-white/80 text-xs">
          <span>{clip.views > 999 ? `${(clip.views/1000).toFixed(1)}k` : clip.views} views</span>
          <span className="hidden md:inline">{clip.createdAt}</span>
        </div>
      </div>
    </div>
  );
};

const TikTokFeed: React.FC<TikTokFeedProps> = ({ clips, onEdit, onShare, onDownload }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isMuted, setIsMuted] = useState(true);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleScroll = useCallback(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    const scrollTop = container.scrollTop;
    const itemHeight = container.clientHeight;
    const newActiveIndex = Math.round(scrollTop / itemHeight);
    
    if (newActiveIndex !== activeIndex && newActiveIndex < clips.length) {
      setActiveIndex(newActiveIndex);
    }
  }, [activeIndex, clips.length]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  if (clips.length === 0) {
    return (
      <div className="w-full max-w-sm md:max-w-md mx-auto h-[500px] md:h-[600px] flex items-center justify-center px-4">
        <div className="text-center">
          <Play className="h-8 w-8 md:h-12 md:w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-base md:text-lg font-medium mb-2">No clips available</h3>
          <p className="text-sm md:text-base text-muted-foreground">Create your first clip to get started</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-sm md:max-w-md mx-auto px-2 md:px-0">
      <div
        ref={containerRef}
        className="h-[500px] md:h-[600px] lg:h-[700px] overflow-y-auto snap-y snap-mandatory scrollbar-hide"
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
      >
        {clips.map((clip, index) => (
          <div key={clip.id} className="h-full snap-start flex items-center justify-center p-2 md:p-4">
            <VideoPlayer
              clip={clip}
              isActive={index === activeIndex}
              isMuted={isMuted}
              onToggleMute={() => setIsMuted(!isMuted)}
              onEdit={onEdit}
              onShare={onShare}
              onDownload={onDownload}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default TikTokFeed;

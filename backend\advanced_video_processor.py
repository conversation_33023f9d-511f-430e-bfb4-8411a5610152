"""
Advanced Video Processing Module for SmartClips
Provides comprehensive video enhancement with subtitles, emojis, clipart, and short-form content creation
Updated with enhanced TikTok-style subtitle system, PROPER WORD SPACING, and AI-POWERED FEATURES
"""
import whisper
import pysrt
from transformers import pipeline
import openai
import emoji
from textblob import TextBlob
import spacy
import nltk
import os
import re
import json
import tempfile
import subprocess
import logging
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path
import requests
from datetime import datetime, timedelta

# Core processing libraries
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import librosa
from moviepy.editor import <PERSON>FileClip, AudioFileClip, ImageClip, CompositeVideoClip, TextClip, ColorClip
import moviepy.config as mpconfig
mpconfig.change_settings(
    {"IMAGEMAGICK_BINARY": r"C:\Program Files\ImageMagick-7.1.2-Q16-HDRI\magick.exe"})


logger = logging.getLogger(__name__)


class AdvancedVideoProcessor:
    """
    Comprehensive video processor that adds subtitles, emojis, clipart, and creates short-form content
    Enhanced with AI-powered emoji selection and importance detection
    """

    def __init__(self, openai_api_key: str = None, temp_dir: str = None):
        self.openai_api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        self.temp_dir = temp_dir or tempfile.gettempdir()

        if self.openai_api_key:
            self.openai_client = openai.OpenAI(api_key=self.openai_api_key)
        else:
            self.openai_client = None

        self._init_nlp_models()
        self.emotion_analyzer = None

        self.emotion_emojis = {
            'joy': ['😊', '😄', '🎉', '✨', '💫'], 'sadness': ['😢', '😔', '💔', '😞'],
            'anger': ['😠', '😡', '🔥', '💢'], 'fear': ['😨', '😰', '😱', '🙈'],
            'surprise': ['😲', '😮', '🤯', '😯'], 'disgust': ['🤢', '😷', '🤮', '😖'],
            'love': ['❤️', '💕', '😍', '🥰', '💖']
        }

        self.keyword_emojis = {
            'money': '💰', 'win': '🏆', 'success': '🎉', 'happy': '😊', 'great': '👍',
            'love': '❤️', 'fire': '🔥', 'amazing': '🤩', 'wow': '😮', 'idea': '💡',
            'sad': '😢', 'cry': '😭', 'angry': '😠', 'cool': '😎', 'lol': '😂',
            'question': '❓', 'warning': '⚠️', 'check': '✅', 'wrong': '❌'
        }

        self.platform_settings = {
            'tiktok': {'aspect_ratio': (9, 16), 'max_duration': 60, 'resolution': (1080, 1920)},
            'instagram': {'aspect_ratio': (9, 16), 'max_duration': 90, 'resolution': (1080, 1920)},
            'youtube_shorts': {'aspect_ratio': (9, 16), 'max_duration': 60, 'resolution': (1080, 1920)},
            'twitter': {'aspect_ratio': (16, 9), 'max_duration': 140, 'resolution': (1280, 720)}
        }

        self.tiktok_styles = {
            'clean_modern': {
                'fontsize': 30, 'font': 'Impact', 'color': '#FFFFFF', 'stroke_color': "#000000",
                'stroke_width': 1.5, 'position': ('center', 0.6), 'max_words_per_line': 3,
                'text_transform': 'upper', 'animation': 'zoom_bounce', 'accent_color': "#FFFFFF",
                'line_spacing': -5, 'word_spacing': 5, 'kerning': 1,
                'shadow_offset': (4, 4),
                'shadow_blur_radius': 3
            },
            'viral_style': {
                'fontsize': 50,                 # Increased font size for impact   80
                'font': 'Impact',               # This font is ideal for the style
                'color': "#FFFFFF",              # Bright yellow for the main text color
                'stroke_color': "#070707",      # Black outline
                'stroke_width': 1,              # Made the outline much thicker
                'position': ('center', 0.55),   # Adjusted vertical position
                'max_words_per_line': 3,        # Shorter lines
                'text_transform': 'upper',      # ALL CAPS
                'animation': 'bounce_in',
                'accent_color': '#FFFFFF',      # Set accent color to white for the other text
                'line_spacing': -15,            # Tighter line spacing
                'word_spacing': 5,
                'kerning': 1,
                'shadow_offset': (6, 6),        # Enhanced drop shadow
                'shadow_blur_radius': 4
            },
            'modern': {
                'fontsize': 20,  # 32
                'font': 'Impact', 'color': '#FFFFFF', 'stroke_color': "#000000",
                'stroke_width': 1, 'position': ('center', 0.55), 'max_words_per_line': 5,
                'text_transform': 'upper', 'animation': 'fade_slide', 'accent_color': "#DBDBDB",
                'line_spacing': -3, 'word_spacing': 3, 'kerning': 1,
                'shadow_offset': (5, 5),
                'shadow_blur_radius': 3
            }
        }

    def get_emotion_analyzer(self):
        if self.emotion_analyzer is None:
            try:
                self.emotion_analyzer = pipeline(
                    "text-classification", model="j-hartmann/emotion-english-distilroberta-base")
            except Exception as e:
                logger.warning(f"Failed to load emotion analyzer: {e}")
                self.emotion_analyzer = False
        return self.emotion_analyzer if self.emotion_analyzer is not False else None

    def _init_nlp_models(self):
        try:
            nltk.download('punkt', quiet=True)
            nltk.download('averaged_perceptron_tagger', quiet=True)
            nltk.download('wordnet', quiet=True)
            nltk.download('stopwords', quiet=True)
            try:
                self.nlp = spacy.load("en_core_web_sm")
            except OSError:
                logger.warning(
                    "spaCy model not found. Install with: python -m spacy download en_core_web_sm")
                self.nlp = None
        except Exception as e:
            logger.error(f"Error initializing NLP models: {e}")
            self.nlp = None

    def extract_transcript_with_timing(self, video_path: str) -> List[Dict[str, Any]]:
        import whisper
        temp_audio_path = None
        try:
            with VideoFileClip(video_path) as video_clip:
                logger.info("Extracting audio from video with MoviePy...")
                temp_audio_path = os.path.join(
                    self.temp_dir, f"temp_audio_{Path(video_path).stem}.wav")
                video_clip.audio.write_audiofile(
                    temp_audio_path, codec='pcm_s16le')
                logger.info(
                    f"Audio extracted to temporary file: {temp_audio_path}")
            model = whisper.load_model("base")
            logger.info("Transcribing audio with Whisper...")
            result = model.transcribe(
                temp_audio_path, language="en", word_timestamps=True)
            transcript_data = []
            if result and 'segments' in result:
                for segment in result['segments']:
                    for word_info in segment.get('words', []):
                        transcript_data.append({
                            'word': word_info['word'].strip(), 'start': word_info['start'],
                            'end': word_info['end'], 'confidence': word_info.get('probability', 1.0)
                        })
            if not transcript_data:
                logger.warning(
                    "Whisper returned no words. The video might be silent.")
            return transcript_data
        except Exception as e:
            logger.error(f"Error extracting transcript: {e}", exc_info=True)
            return []
        finally:
            if temp_audio_path and os.path.exists(temp_audio_path):
                try:
                    os.remove(temp_audio_path)
                except Exception as e:
                    logger.error(
                        f"Failed to delete temp audio file {temp_audio_path}: {e}")

    def analyze_content_with_ai(self, transcript_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        segments = self._group_words_into_segments(transcript_data)
        for segment in segments:
            if not segment['words']:
                continue
            segment_text = ' '.join([word['word']
                                    for word in segment['words']])
            try:
                ai_analysis = self._get_ai_content_analysis(segment_text)
                last_word = segment['words'][-1]
                if ai_analysis.get('emoji'):
                    last_word['emoji'] = ai_analysis['emoji']
                if ai_analysis.get('is_important', False):
                    for word_data in segment['words']:
                        word_data['is_important'] = True
            except Exception as e:
                logger.error(f"Error in AI analysis for segment: {e}")
        return transcript_data

    def _get_ai_content_analysis(self, text: str) -> Dict[str, Any]:
        if not self.openai_api_key or not self.openai_client:
            return {}
        try:
            prompt = f'Analyze this text for video subtitles: "{text}". Provide a JSON response with "emoji" (a single relevant emoji or null) and "is_important" (boolean). Return only valid JSON.'
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo", messages=[{"role": "user", "content": prompt}], max_tokens=100, temperature=0.2
            )
            return json.loads(response.choices[0].message.content.strip())
        except Exception as e:
            logger.error(f"Error in OpenAI analysis: {e}")
            return {}

    def _group_words_into_segments(self, transcript_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        segments, current_segment = [], {
            'words': [], 'start': None, 'end': None}
        for word_data in transcript_data:
            if current_segment['start'] is None:
                current_segment['start'] = word_data['start']
            current_segment['words'].append(word_data)
            current_segment['end'] = word_data['end']
            if len(current_segment['words']) >= 15 or word_data['word'].endswith(('.', '!', '?')):
                if current_segment['words']:
                    segments.append(current_segment)
                current_segment = {'words': [], 'start': None, 'end': None}
        if current_segment['words']:
            segments.append(current_segment)
        return segments

    def _add_fallback_emojis(self, transcript_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        sentences = self._group_words_into_segments(transcript_data)
        emotion_analyzer = self.get_emotion_analyzer()
        for sentence_data in sentences:
            if not sentence_data['words']:
                continue
            last_word = sentence_data['words'][-1]
            if 'emoji' in last_word:
                continue
            sentence_text = ' '.join(word['word'].lower()
                                     for word in sentence_data['words'])
            emoji_added = False
            for keyword, emoji in self.keyword_emojis.items():
                if f' {keyword} ' in f' {sentence_text} ':
                    last_word['emoji'] = emoji
                    logger.info(
                        f"Added emoji '{emoji}' based on keyword '{keyword}'.")
                    emoji_added = True
                    break
            if emoji_added:
                continue
            if emotion_analyzer:
                try:
                    result = emotion_analyzer(sentence_text)[0]
                    if result['score'] > 0.5 and result['label'] in self.emotion_emojis:
                        emoji_choice = np.random.choice(
                            self.emotion_emojis[result['label']])
                        last_word['emoji'] = emoji_choice
                        logger.info(
                            f"Added emoji '{emoji_choice}' from ML (Emotion: {result['label']}, Conf: {result['score']:.2f}).")
                except Exception as e:
                    logger.warning(f"ML emotion analysis failed: {e}")
        return transcript_data

    def create_emoji_overlay_clips(self, transcript_data: List[Dict[str, Any]], video_size: Tuple[int, int]) -> List:
        """
        Creates full-color emoji overlays by first rendering them to a transparent PNG image,
        then using ImageClip. This bypasses TextClip's color limitations.
        """
        emoji_clips = []
        emoji_y_position = 0.48

        # Load an emoji-supporting font for Pillow
        try:
            # On Windows, this font is usually available.
            font_path = "C:/Windows/Fonts/seguiemj.ttf"
            if not os.path.exists(font_path):
                # Fallback for other systems or if font is missing
                font_path = "Arial.ttf"
            emoji_font = ImageFont.truetype(font_path, size=80)
        except IOError:
            logger.warning(
                "Segoe UI Emoji font not found. Emoji rendering might be black and white.")
            emoji_font = ImageFont.load_default()

        for i, word_data in enumerate(transcript_data):
            if word_data.get('emoji') and word_data.get('start') is not None:
                try:
                    emoji_char = word_data['emoji']

                    # --- THIS IS THE FIX ---
                    temp_emoji_path = os.path.join(
                        self.temp_dir, f"temp_emoji_{i}.png")

                    bbox = emoji_font.getbbox(emoji_char)
                    image_width = bbox[2] - bbox[0]
                    image_height = bbox[3] - bbox[1]

                    padding = 10
                    img = Image.new(
                        'RGBA', (image_width + padding*2, image_height + padding*2), (255, 255, 255, 0))
                    draw = ImageDraw.Draw(img)

                    draw.text((padding - bbox[0], padding - bbox[1]), emoji_char,
                              font=emoji_font, embedded_color=True, fill=(0, 0, 0, 255))

                    img.save(temp_emoji_path)

                    emoji_clip = ImageClip(
                        temp_emoji_path, duration=1.5).set_start(word_data['start'])

                    emoji_clip = emoji_clip.set_position(
                        ('center', emoji_y_position), relative=True)
                    emoji_clip = self._animate_emoji(emoji_clip)
                    emoji_clips.append(emoji_clip)

                except Exception as e:
                    logger.error(
                        f"Error creating emoji overlay for '{word_data.get('emoji')}': {e}", exc_info=True)

        logger.info(f"Created {len(emoji_clips)} emoji overlay clips.")
        return emoji_clips

    def _animate_emoji(self, emoji_clip):
        try:
            def size_func(t): return 0.5 + 0.5 * (t / 0.3) + \
                0.2 * np.sin(10 * t) if t < 0.3 else 1.0
            emoji_clip = emoji_clip.resize(
                size_func).crossfadein(0.3).crossfadeout(0.5)
            return emoji_clip
        except Exception as e:
            logger.error(f"Error animating emoji: {e}")
            return emoji_clip.crossfadein(0.2).crossfadeout(0.3)

    def _format_text_with_proper_spacing(self, text: str, style_config: Dict[str, Any]) -> str:
        transform = style_config.get('text_transform', 'none')
        if transform == 'upper':
            text = text.upper()
        elif transform == 'title':
            text = text.title()
        elif transform == 'lower':
            text = text.lower()
        words, lines, current_line = text.strip().split(), [], []
        max_words = style_config.get('max_words_per_line', 4)
        for word in words:
            current_line.append(word)
            if len(current_line) >= max_words:
                lines.append(self._add_word_spacing(
                    current_line, style_config))
                current_line = []
        if current_line:
            lines.append(self._add_word_spacing(current_line, style_config))
        return '\n'.join(lines)

    def _add_word_spacing(self, words: List[str], style_config: Dict[str, Any]) -> str:
        word_spacing = style_config.get('word_spacing', 8)
        if word_spacing >= 10:
            spacer = '   '
        elif word_spacing >= 6:
            spacer = '  '
        else:
            spacer = ' '
        return spacer.join(words)

    def _apply_tiktok_animation(self, clip, animation_type: str):
        try:
            if animation_type == 'bounce_in':
                clip = clip.crossfadein(0.2)

                def bounce_func(t): return 1 + 0.15 * \
                    np.exp(-4*t) * np.sin(12*t) if t < 0.6 else 1
                clip = clip.resize(bounce_func)
            else:
                clip = clip.crossfadein(0.2).crossfadeout(0.2)
            return clip
        except Exception as e:
            logger.error(f"Error applying animation {animation_type}: {e}")
            return clip.crossfadein(0.2).crossfadeout(0.2)

    def _create_single_color_text(self, text: str, style_config: Dict[str, Any],
                                  start_time: float, duration: float, video_size: Tuple[int, int]):
        """
        Creates a text clip with a soft drop shadow using a reliable Pillow-based blur method.
        This version fixes the 'AssertionError' by correctly creating the mask clip.
        """
        from PIL import Image, ImageFilter

        formatted_text = text if '  ' in text else self._format_text_with_proper_spacing(
            text, style_config)

        shadow_offset = style_config.get('shadow_offset', (3, 3))
        blur_radius = style_config.get('shadow_blur_radius', 2)

        text_params = {
            "txt": formatted_text,
            "fontsize": style_config['fontsize'],
            "font": style_config['font'],
            "method": 'caption',
            "size": (int(video_size[0] * 0.9), None),
            "align": 'center',
            "interline": style_config.get('line_spacing', -5)
        }

        shadow_clip_raw = TextClip(color='black', **text_params)

        shadow_mask_np = shadow_clip_raw.get_frame(0)[:, :, 0] > 0
        shadow_pil = Image.fromarray(shadow_mask_np.astype('uint8') * 255)
        shadow_pil_blurred = shadow_pil.filter(
            ImageFilter.GaussianBlur(radius=blur_radius))

        blurred_mask_clip = ImageClip(
            np.array(shadow_pil_blurred), ismask=True)

        shadow_color_clip = ColorClip(
            size=shadow_clip_raw.size, color=(0, 0, 0)).set_opacity(0.5)
        shadow_clip = shadow_color_clip.set_mask(blurred_mask_clip)

        text_clip = TextClip(
            color=style_config['color'],
            stroke_color=style_config['stroke_color'],
            stroke_width=style_config['stroke_width'],
            **text_params
        )

        final_clip = CompositeVideoClip([
            shadow_clip.set_position(shadow_offset),
            text_clip.set_position((0, 0))
        ], size=shadow_clip.size)

        position = style_config['position']
        if isinstance(position[1], float):
            y_pos = int(video_size[1] * position[1])
            final_clip = final_clip.set_position(('center', y_pos))
        else:
            final_clip = final_clip.set_position(position)

        return final_clip.set_start(start_time).set_duration(duration)

    def create_ai_enhanced_subtitles(self, video_path: str, transcript_data: List[Dict[str, Any]],
                                     output_path: str, style: str = "clean_modern",
                                     enable_emoji_overlays: bool = True,
                                     enable_importance_highlighting: bool = True) -> str:
        import subprocess
        import os
        from moviepy.video.io.ffmpeg_writer import ffmpeg_write_video
        video, final_visuals, temp_video_path = None, None, None
        try:
            video = VideoFileClip(video_path, fps_source='fps')
            if not video:
                raise ValueError("MoviePy could not open video file.")
            output_fps = int(round(video.fps if video.fps else 24))
            logger.info(
                f"Using rounded integer FPS: {output_fps} for final render.")

            clips_to_composite = [video.without_audio()]
            style_config = self.tiktok_styles.get(
                style, self.tiktok_styles['clean_modern'])

            if enable_emoji_overlays:
                emoji_clips = self.create_emoji_overlay_clips(
                    transcript_data, video.size)
                if emoji_clips:
                    clips_to_composite.extend(emoji_clips)

            subtitle_chunks = self._create_subtitle_chunks(
                transcript_data, style_config)
            for chunk in subtitle_chunks:
                txt_clip = self._create_single_color_text(
                    chunk['text'], style_config, chunk['start'], chunk['duration'], video.size)
                animated_clip = self._apply_tiktok_animation(
                    txt_clip, style_config['animation'])
                clips_to_composite.append(animated_clip)

            final_visuals = CompositeVideoClip(
                clips_to_composite, size=video.size).set_fps(output_fps)
            temp_video_path = os.path.join(
                self.temp_dir, f"temp_silent_{Path(output_path).name}")
            ffmpeg_write_video(final_visuals, temp_video_path,
                               fps=output_fps, codec='libx264', threads=4, preset='fast')

            ffmpeg_command = ['ffmpeg', '-y', '-i', temp_video_path, '-i',
                              video_path, '-c:v', 'copy', '-c:a', 'aac', '-shortest', output_path]
            subprocess.run(ffmpeg_command, check=True,
                           stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            logger.info(f"Successfully created final video at: {output_path}")
            return output_path
        except Exception as e:
            if isinstance(e, subprocess.CalledProcessError):
                logger.error(
                    f"FFmpeg command failed. Stderr: {e.stderr.decode()}")
            else:
                logger.error(
                    f"FATAL error in create_ai_enhanced_subtitles: {e}", exc_info=True)
            raise
        finally:
            if video:
                video.close()
            if final_visuals:
                final_visuals.close()
            if temp_video_path and os.path.exists(temp_video_path):
                try:
                    os.remove(temp_video_path)
                except Exception as e:
                    logger.error(
                        f"Failed to clean up temp file {temp_video_path}: {e}")

    def _create_subtitle_chunks(self, transcript_data: List[Dict[str, Any]], style_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        chunks, current_chunk = [], {
            'words': [], 'start': None, 'end': None, 'text': '', 'word_data': []}
        max_words = style_config.get('max_words_per_line', 4)
        valid_words = [w for w in transcript_data if w.get(
            'start') is not None and w.get('end') is not None and w['end'] > w['start']]

        if not valid_words:
            logger.warning(
                "No valid words with positive duration found in transcript for chunking.")
            return []

        for word_data in valid_words:
            if current_chunk['start'] is None:
                current_chunk['start'] = word_data['start']
            current_chunk['words'].append(word_data['word'])
            current_chunk['word_data'].append(word_data)
            current_chunk['end'] = word_data['end']

            if len(current_chunk['words']) >= max_words or word_data['word'].endswith(('.', '!', '?', ',')):
                duration = current_chunk['end'] - current_chunk['start']
                if duration > 0:
                    text = ' '.join(current_chunk['words'])
                    last_word_in_chunk = current_chunk['word_data'][-1]
                    if 'emoji' in last_word_in_chunk:
                        text += f" {last_word_in_chunk['emoji']}"
                        logger.info(
                            f"Appending emoji '{last_word_in_chunk['emoji']}' to subtitle chunk text.")

                    current_chunk['duration'] = duration
                    current_chunk['text'] = text
                    chunks.append(current_chunk)
                current_chunk = {'words': [], 'start': None,
                                 'end': None, 'text': '', 'word_data': []}

        if current_chunk['words']:
            duration = current_chunk['end'] - current_chunk['start']
            if duration > 0:
                text = ' '.join(current_chunk['words'])
                last_word_in_chunk = current_chunk['word_data'][-1]
                if 'emoji' in last_word_in_chunk:
                    text += f" {last_word_in_chunk['emoji']}"
                    logger.info(
                        f"Appending emoji '{last_word_in_chunk['emoji']}' to final subtitle chunk text.")

                current_chunk['duration'] = duration
                current_chunk['text'] = text
                chunks.append(current_chunk)

        return chunks

    def process_video_comprehensive(self, video_path: str, output_dir: str,
                                    options: Dict[str, Any] = None) -> Dict[str, Any]:
        if options is None:
            options = {}
        add_subtitles = options.get('add_subtitles', True)
        add_emojis = options.get('add_emojis', True)
        subtitle_style = options.get('subtitle_style', 'clean_modern')
        use_ai_features = options.get('use_ai_features', False)
        results = {'original_video': video_path, 'processed_videos': {
        }, 'metadata': {}, 'status': 'Processing', 'message': ''}
        start_time = datetime.now()
        try:
            logger.info(
                f"Starting comprehensive AI video processing for: {video_path}")
            transcript_data = self.extract_transcript_with_timing(video_path)
            if not transcript_data:
                results.update({'status': 'Completed with Warning',
                               'message': 'No audio detected or video is silent.'})
                return results

            if use_ai_features and self.openai_api_key:
                logger.info("Stage 1: Analyzing content with primary AI...")
                transcript_data = self.analyze_content_with_ai(transcript_data)
                logger.info(
                    f"Found {sum(1 for w in transcript_data if 'emoji' in w)} emojis from primary AI.")
            if add_emojis:
                logger.info("Stage 2: Running robust fallback emoji system...")
                transcript_data = self._add_fallback_emojis(transcript_data)
            logger.info(
                f"Total emojis in transcript after all stages: {sum(1 for w in transcript_data if 'emoji' in w)}")
            results['metadata']['transcript'] = transcript_data

            if add_subtitles:
                logger.info("Creating AI-enhanced animated subtitles...")
                subtitled_path = os.path.join(
                    output_dir, f"video_with_ai_subtitles_{Path(video_path).stem}.mp4")
                self.create_ai_enhanced_subtitles(
                    video_path, transcript_data, subtitled_path, subtitle_style,
                    enable_emoji_overlays=add_emojis,
                    enable_importance_highlighting=use_ai_features
                )
                results['processed_videos']['with_ai_subtitles'] = subtitled_path
            results.update(
                {'status': 'Success', 'message': 'Video processed successfully.'})
        except Exception as e:
            logger.error(
                f"FATAL error in comprehensive video processing: {e}", exc_info=True)
            results.update({'status': 'Error', 'message': str(e)})
        finally:
            results['processing_time'] = (
                datetime.now() - start_time).total_seconds()
            logger.info(
                f"Processing finished in {results['processing_time']:.2f}s. Status: {results['status']}")
            return results


def process_video_with_enhancements(video_path: str, output_dir: str,
                                    openai_api_key: str = None,
                                    options: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Convenience function to process a video with all AI enhancements
    """
    processor = AdvancedVideoProcessor(openai_api_key=openai_api_key)
    return processor.process_video_comprehensive(video_path, output_dir, options)


def create_quick_ai_tiktok_clip(video_path: str, output_path: str,
                                openai_api_key: str = None) -> str:
    """
    Quick function to create a TikTok-ready clip with AI-powered features
    """
    processor = AdvancedVideoProcessor(openai_api_key=openai_api_key)

    transcript_data = processor.extract_transcript_with_timing(video_path)

    if openai_api_key:
        transcript_data = processor.analyze_content_with_ai(transcript_data)
    else:
        transcript_data = processor.analyze_emotions_and_add_emojis(
            transcript_data)

    processor.create_ai_enhanced_subtitles(
        video_path,
        transcript_data,
        output_path,
        'clean_modern',
        enable_emoji_overlays=True,
        enable_importance_highlighting=True
    )

    return output_path


def create_video_with_emoji_overlays_only(video_path: str, output_path: str,
                                          openai_api_key: str = None,
                                          style: str = "clean_modern") -> str:
    """
    Create a video with only emoji overlays (no importance highlighting)
    """
    processor = AdvancedVideoProcessor(openai_api_key=openai_api_key)

    # Extract transcript
    transcript_data = processor.extract_transcript_with_timing(video_path)

    # Add emojis
    if openai_api_key:
        transcript_data = processor.analyze_content_with_ai(transcript_data)
    else:
        transcript_data = processor.analyze_emotions_and_add_emojis(
            transcript_data)

    # Create video with only emoji overlays
    processor.create_ai_enhanced_subtitles(
        video_path,
        transcript_data,
        output_path,
        style,
        enable_emoji_overlays=True,
        enable_importance_highlighting=False

    )

    return output_path


def create_video_with_importance_highlighting_only(video_path: str, output_path: str,
                                                   openai_api_key: str = None,
                                                   style: str = "clean_modern") -> str:
    """
    Create a video with only importance highlighting (no emoji overlays)
    """
    processor = AdvancedVideoProcessor(openai_api_key=openai_api_key)

    # Extract transcript
    transcript_data = processor.extract_transcript_with_timing(video_path)

    # AI analysis for importance detection
    if openai_api_key:
        transcript_data = processor.analyze_content_with_ai(transcript_data)

    # Create video with only importance highlighting
    processor.create_ai_enhanced_subtitles(
        video_path,
        transcript_data,
        output_path,
        style,
        enable_emoji_overlays=False,
        enable_importance_highlighting=True
    )

    return output_path


def analyze_video_importance(video_path: str, openai_api_key: str = None) -> Dict[str, Any]:
    """
    Analyze a video to detect important segments that would be highlighted
    """
    processor = AdvancedVideoProcessor(openai_api_key=openai_api_key)

    # Extract transcript
    transcript_data = processor.extract_transcript_with_timing(video_path)

    # AI analysis
    if openai_api_key:
        transcript_data = processor.analyze_content_with_ai(transcript_data)

    # Detect important segments
    important_segments = processor.detect_important_text_segments(
        transcript_data)

    return {
        'total_words': len(transcript_data),
        'important_segments': important_segments,
        'importance_count': len(important_segments),
        'ai_features_used': bool(openai_api_key),
        'video_duration': transcript_data[-1]['end'] if transcript_data else 0
    }

import React, { useEffect, useState, useRef } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import { CheckCircle, Loader2 } from "lucide-react";
import { supabase } from "@/lib/supabase";

const PaymentSuccess: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user, updateProfile, isLoading } = useAuth();
  const [isProcessing, setIsProcessing] = useState(true);
  const [paymentDetails, setPaymentDetails] = useState<{
    paymentId: string | null;
    plan: string | null;
  }>({ paymentId: null, plan: null });
  const [error, setError] = useState<string | null>(null);
  const effectRan = useRef(false);

  useEffect(() => {
    if (isLoading) {
      console.log("Auth is loading, waiting...");
      return;
    }

    // 2. PREVENT the effect from running more than once.
    if (effectRan.current === true) {
      return;
    }
    effectRan.current = true;

    // We define an async function inside so we can use await.
    const processPayment = async () => {
      console.log("--- Auth has loaded. Processing payment redirect ONCE. ---");

      const sessionId = searchParams.get("session_id");
      const planName = searchParams.get("plan");

      if (!sessionId) {
        // Only session ID is absolutely required now
        setError("Payment session information is missing in the URL.");
        setIsProcessing(false);
        return;
      }

      setPaymentDetails({ paymentId: sessionId, plan: planName });

      const stripeKey = import.meta.env.VITE_STRIPE_SECRET_KEY;
      console.log("Stripe key:", stripeKey);
      const response = await fetch(
        `https://api.stripe.com/v1/checkout/sessions/${sessionId}`,
        { headers: { Authorization: `Bearer ${stripeKey}` } }
      );
      const sessionDetails = await response.json();
      const stripeCustomerId = sessionDetails.customer;

      if (!stripeCustomerId) {
        setError("Could not verify payment session. Please contact support.");
        setIsProcessing(false);
        return;
      }
      // 3. NOW we can safely check for the user.
      if (user) {
        console.log(`User ${user.id} is present. Updating profile...`);
        await updateUserSubscription(planName, stripeCustomerId);
      } else {
        console.error(
          "Auth loaded, but no user or planName found after payment verification."
        );
        setError(
          "Payment processed, but you were not logged in. Please log in and contact support."
        );
      }

      navigate("/payment-success", { replace: true });
      setIsProcessing(false);
    };

    processPayment();

    // We only depend on `isLoading` to trigger this effect when auth state is ready.
  }, [isLoading, user, searchParams, navigate]);

  // Inside the PaymentSuccess component

  const updateUserSubscription = async (
    planName: string,
    stripeCustomerId: string
  ) => {
    if (!user) {
      console.error("Cannot update: User not found.");
      setError(
        "Your session expired. Please log in again and contact support to apply your purchase."
      );
      return;
    }

    const currentCredits = user.credits || 0;
    console.log(`User's current credits: ${currentCredits}`);

    let creditsToAdd = 0;
    let subscriptionPlanName = "free";

    switch (planName.toLowerCase()) {
      case "basic":
        creditsToAdd = 200;
        subscriptionPlanName = "basic";
        break;
      case "pro":
        creditsToAdd = 500;
        subscriptionPlanName = "pro";
        break;
      case "enterprise":
        creditsToAdd = 999999 - currentCredits; // Unlimited credits
        subscriptionPlanName = "enterprise";
        break;
      default:
        console.error(`Unknown plan name received: ${planName}`);
        setError(
          `An unknown plan (${planName}) was processed. Please contact support.`
        );
        return;
    }

    // 3. CALCULATE the new total.
    const newTotalCredits = Math.round(currentCredits + creditsToAdd);
    console.log(
      `Adding ${creditsToAdd} credits for a new total of: ${newTotalCredits}`
    );

    try {
      // await updateProfile({
      //   subscription: subscriptionPlanName,
      //   credits: newTotalCredits,
      //   second: newTotalCredits * 60,
      //   stripe_customer_id: stripeCustomerId,
      // });

      // console.log("User profile updated successfully via AuthContext.");
      const profileUpdates = {
        subscription: subscriptionPlanName,
        credits: newTotalCredits,
        second: newTotalCredits * 60,
        stripe_customer_id: stripeCustomerId,
      };

      await updateProfile(profileUpdates);
      console.log("Profile updated in database and React state.");

      // Save the updated user object from the parent scope (after updateProfile)
      localStorage.setItem(
        "userProfile",
        JSON.stringify({ ...user, ...profileUpdates })
      );
      console.log("localStorage 'userProfile' has been explicitly updated. ", {
        ...user,
        ...profileUpdates,
      });
    } catch (error) {
      console.error("Failed to update user subscription:", error);
      setError(
        "Your payment was successful, but we failed to update your account. Please contact support."
      );
    }
  };

  const handleContinue = () => {
    navigate("/dashboard");
  };

  if (isProcessing) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-white mx-auto mb-4" />
          <p className="text-white">Processing your payment...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg p-8 text-center">
        <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-6" />

        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          Payment Successful!
        </h1>

        <p className="text-gray-600 mb-6">
          Thank you for your purchase. Your subscription has been activated.
        </p>

        <Button
          onClick={handleContinue}
          className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:opacity-90"
        >
          Continue to Dashboard
        </Button>

        <p className="text-xs text-gray-500 mt-4">
          You will receive a confirmation email shortly.
        </p>
      </div>
    </div>
  );
};

export default PaymentSuccess;

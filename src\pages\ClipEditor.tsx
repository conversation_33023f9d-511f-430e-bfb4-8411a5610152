import React, { useState, useEffect, useRef, useMemo } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  Download,
  Play,
  Pause,
  Save,
  Type,
  Palette,
  Volume2,
  Scissors,
  RotateCcw,
  Share2,
  Settings,
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import DashboardLayout from "@/components/Dashboard";
import Timeline from "@/components/editor/Timeline";
import { useAuth } from "@/context/AuthContext";
import { Navigate } from "react-router-dom";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { supabase } from "@/lib/supabase";
import {
  saveProject,
  updateProject,
  exportProject,
  addClipToTimeline,
  updateTimelineClip,
  deleteTimelineClip,
  splitTimelineClip,
  duplicateTimelineClip,
  addTextOverlay,
  updateTextOverlay,
  deleteTextOverlay,
  type EditorProject,
  type TimelineClip as APITimelineClip
} from "@/services/editorService";

interface TimelineClip {
  id: string;
  start: number;
  end: number;
  duration: number;
  url: string;
  title: string;
  track: number;
  type: "video" | "gap";
  filter: string;
}

interface TextOverlay {
  id: string;
  text: string;
  x: number;
  y: number;
  fontSize: number;
  color: string;
  startTime: number;
  endTime: number;
}

interface TimelineSegment {
  id: string;
  sourceStart: number;
  sourceEnd: number;
  duration: number;
  virtualStart: number;
  virtualEnd: number;
}

const ClipEditor = () => {
  const { isAuthenticated } = useAuth();
  const { clipId } = useParams();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(60);
  const [virtualTime, setVirtualTime] = useState(0);
  const [sourceDuration, setSourceDuration] = useState(60);
  const [activeFilter, setActiveFilter] = useState("none");

  // Timeline state
  const [clips, setClips] = useState<TimelineClip[]>([]);
  const [textOverlays, setTextOverlays] = useState<TextOverlay[]>([]);
  // const videoRef = useRef<HTMLVideoElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Editing state
  const [clipTitle, setClipTitle] = useState("");
  const [clipDescription, setClipDescription] = useState("");
  const [selectedTextOverlay, setSelectedTextOverlay] = useState<string | null>(
    null
  );
  const [newTextOverlay, setNewTextOverlay] = useState({
    text: "",
    fontSize: 24,
    color: "#ffffff",
    startTime: 0,
    endTime: 5,
  });

  const { timelineSegments, virtualDuration } = useMemo(() => {
    let currentVirtualTime = 0;
    const segments: TimelineSegment[] = [];

    for (const clip of clips) {
      // Use the `clips` array directly
      const duration = clip.end - clip.start;
      if (duration <= 0) continue; // Skip invalid clips

      segments.push({
        id: clip.id,
        sourceStart: clip.start,
        sourceEnd: clip.end,
        duration: duration,
        virtualStart: currentVirtualTime,
        virtualEnd: currentVirtualTime + duration,
        type: clip.type,
      });
      currentVirtualTime += duration;
    }

    const finalVirtualDuration = Math.max(currentVirtualTime, sourceDuration);

    return {
      timelineSegments: segments,
      virtualDuration: finalVirtualDuration,
    };
  }, [clips, sourceDuration]);

  const mapVirtualToSourceTime = (vTime: number): number | null => {
    const segment = timelineSegments.find(
      (s) => vTime >= s.virtualStart && vTime < s.virtualEnd
    );
    if (!segment) return null;

    const offset = vTime - segment.virtualStart;
    return segment.sourceStart + offset;
  };

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  useEffect(() => {
    loadClip();
  }, [clipId]);

  const loadClip = async () => {
    try {
      setLoading(true);

      const { data, error } = await supabase
        .from("myclip")
        .select("*")
        .eq("id", clipId)
        .single();

      if (error || !data) {
        throw error || new Error("Clip not found");
      }

      const fetchedClip = {
        id: data.id,
        title: data.title || "",
        description: data.text || "",
        url: data.url,
        duration: data.end_time - data.start_time,
        vitalityScore: data.score || 0,
      };

      const fetchedTimelineClip: TimelineClip = {
        id: "main-clip",
        start: data.start_time,
        end: data.end_time,
        duration: data.end_time - data.start_time,
        url: data.url,
        title: data.title || "Clip",
        track: 0,
        type: "video",
        filter: "none",
      };

      setClipTitle(fetchedClip.title);
      setClipDescription(fetchedClip.description);
      setDuration(fetchedClip.duration);
      setSourceDuration(fetchedClip.duration);
      setClips([fetchedTimelineClip]);
      setTextOverlays([]);
    } catch (error) {
      console.error("Error loading clip:", error);
      toast({
        title: "Error loading clip",
        description: "Failed to load the clip for editing",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // --- FILTER STYLES ---
  const getFilterStyle = (filterName: string) => {
    switch (filterName) {
      case "grayscale":
        return { filter: "grayscale(100%)" };
      case "sepia":
        return { filter: "sepia(100%)" };
      case "invert":
        return { filter: "invert(100%)" };
      case "blur":
        return { filter: "blur(5px)" };
      default:
        return { filter: "none" };
    }
  };

  // --- TIME UPDATE HANDLER ---

  const handleTimeUpdate = () => {
    if (!videoRef.current || !isPlaying) return;

    const segment = timelineSegments.find(
      (s) => virtualTime >= s.virtualStart && virtualTime < s.virtualEnd
    );

    // Handle gaps or end of timeline
    if (!segment || segment.type === "gap") {
      if (activeFilter !== "none") setActiveFilter("none");

      const nextSegment = timelineSegments.find(
        (s) => s.virtualStart >= virtualTime && s.type === "video"
      );

      if (nextSegment) {
        handleTimeChange(nextSegment.virtualStart);
      } else {
        handlePause();
        if (virtualTime >= virtualDuration - 0.1) {
          handleTimeChange(0); // Loop to start
        }
      }
      return;
    }

    // Handle video segments
    if (segment.type === "video") {
      const clip = clips.find((c) => c.id === segment.id);
      if (clip && clip.filter !== activeFilter) {
        setActiveFilter(clip.filter);
      }

      const sourceTime = videoRef.current.currentTime;
      const segmentEnd = segment.sourceEnd - 0.1; // Small buffer

      // Check if we need to move to next segment
      if (sourceTime >= segmentEnd) {
        const nextSegment =
          timelineSegments[timelineSegments.indexOf(segment) + 1];
        if (nextSegment) {
          handleTimeChange(nextSegment.virtualStart);
        } else {
          handlePause();
          handleTimeChange(0); // Reset to start
        }
      } else {
        // Update virtual time normally
        const offset = sourceTime - segment.sourceStart;
        setVirtualTime(segment.virtualStart + offset);
      }
    }
  };

  const handleTextOverlayUpdate = (
    overlayId: string,
    updates: Partial<TextOverlay>
  ) => {
    setTextOverlays((prev) =>
      prev.map((overlay) =>
        overlay.id === overlayId ? { ...overlay, ...updates } : overlay
      )
    );
  };

  const handlePlay = () => {
    console.log("Playing video at virtual time:", virtualTime);
    if (!videoRef.current || timelineSegments.length === 0) return;
    console.log("Starting playback with virtual time:", virtualTime);
    if (virtualTime >= virtualDuration - 0.1) {
      handleTimeChange(0);
    }
    console.log(
      "Video current time before play:",
      videoRef.current.currentTime
    );
    // If paused at the very end, loop to the beginning
    if (virtualTime >= virtualDuration) {
      handleTimeChange(0); // Reset to start
    }
    console.log(
      "Video current time after reset:",
      videoRef.current.currentTime
    );

    setIsPlaying(true);
    videoRef.current.play();
  };

  const handlePause = () => {
    setIsPlaying(false);
    videoRef.current?.pause();
  };
  const handleSpeedChange = (rate: number) => {
    if (videoRef.current) {
      videoRef.current.playbackRate = rate;
    }
  };

  const handleTimeChange = (vTime: number) => {
    const newSourceTime = mapVirtualToSourceTime(vTime);
    if (videoRef.current && newSourceTime !== null) {
      videoRef.current.currentTime = newSourceTime;
    }
    setVirtualTime(vTime);
  };

  const handleClipUpdate = (clipId: string, updates: Partial<TimelineClip>) => {
    setClips((prev) =>
      prev.map((clip) => (clip.id === clipId ? { ...clip, ...updates } : clip))
    );
  };

  const handleClipMove = (draggedClipId: string, targetClipId: string) => {
    if (draggedClipId === targetClipId) return;

    // Immediately pause the player to prevent any playback loops during the state update.
    handlePause();

    const targetSegment = timelineSegments.find((s) => s.id === targetClipId);

    setClips((prevClips) => {
      const draggedIndex = prevClips.findIndex((c) => c.id === draggedClipId);
      const targetIndex = prevClips.findIndex((c) => c.id === targetClipId);

      if (draggedIndex === -1 || targetIndex === -1) {
        return prevClips;
      }

      const newClips = [...prevClips];
      const [draggedItem] = newClips.splice(draggedIndex, 1);
      newClips.splice(targetIndex, 0, draggedItem);

      const finalClips = newClips.filter((clip) => {
        if (clip.id === targetClipId && clip.type === "gap") {
          return false;
        }
        return true;
      });

      return finalClips;
    });

    if (targetSegment) {
      handleTimeChange(targetSegment.virtualStart);
    }

    toast({ title: "Clip moved" });
  };

  const handleClipDelete = (clipId: string) => {
    handlePause();

    setClips((prevClips) =>
      prevClips.map((clip) => {
        if (clip.id === clipId) {
          // If the clip is deleted, we replace it with a gap clip
          return { ...clip, type: "gap", url: null, title: "Empty Gap" };
        }
        return clip;
      })
    );
  };

  const handleClipAdd = (clip: Omit<TimelineClip, "id">) => {
    const newClip: TimelineClip = {
      ...clip,
      id: `clip-${Date.now()}`,
    };
    setClips((prev) => [...prev, newClip]);
  };

  const handleClipDuplicate = (clipId: string) => {
    const clipIndex = clips.findIndex((c) => c.id === clipId);
    if (clipIndex === -1) return;

    const clipToDuplicate = clips[clipIndex];

    const newClip: TimelineClip = {
      ...clipToDuplicate,
      id: `clip-${Date.now()}`,
    };

    const newClips = [
      ...clips.slice(0, clipIndex + 1),
      newClip,
      ...clips.slice(clipIndex + 1),
    ];

    setClips(newClips);
    toast({ title: "Clip duplicated" });
  };

  const handleClipSplit = (clipId: string, time: number) => {
    // The `time` passed in is VIRTUAL time from the playhead.
    const clipIndex = clips.findIndex((c) => c.id === clipId);
    if (clipIndex === -1) return;

    const clipToSplit = clips[clipIndex];

    const sourceSplitTime = mapVirtualToSourceTime(time);

    if (
      sourceSplitTime === null ||
      sourceSplitTime <= clipToSplit.start + 0.1 ||
      sourceSplitTime >= clipToSplit.end - 0.1
    ) {
      toast({
        title: "Cannot split",
        description: "Playhead must be inside the clip.",
      });
      return;
    }

    const firstPart: TimelineClip = {
      ...clipToSplit,
      end: sourceSplitTime,
      duration: sourceSplitTime - clipToSplit.start,
    };

    const secondPart: TimelineClip = {
      ...clipToSplit,
      id: `clip-${Date.now()}`,
      start: sourceSplitTime,
      duration: clipToSplit.end - sourceSplitTime,
    };

    // Ensure the second part has the same properties as the original clip
    const newClips = [
      ...clips.slice(0, clipIndex),
      firstPart,
      secondPart,
      ...clips.slice(clipIndex + 1),
    ];

    setClips(newClips);
    toast({ title: "Clip split" });
  };

  const addTextOverlay = () => {
    if (!newTextOverlay.text.trim()) return;

    const overlay: TextOverlay = {
      id: `text-${Date.now()}`,
      ...newTextOverlay,
      x: 50,
      y: 50,
    };

    setTextOverlays((prev) => [...prev, overlay]);

    // Reset the form for the next overlay. This part is correct.
    setNewTextOverlay({
      text: "",
      fontSize: 24,
      color: "#ffffff",
      startTime: 0,
      endTime: 5,
    });
  };

  const removeTextOverlay = (id: string) => {
    setTextOverlays((prev) => prev.filter((overlay) => overlay.id !== id));
    if (selectedTextOverlay === id) {
      setSelectedTextOverlay(null);
    }
  };

  const handleTextOverlayDelete = (overlayId: string) => {
    setTextOverlays((prev) =>
      prev.filter((overlay) => overlay.id !== overlayId)
    );
    toast({ title: "Text overlay removed" });
  };

  const handleFilterDelete = (clipId: string) => {
    handleClipUpdate(clipId, { filter: "none" });
    toast({ title: "Filter removed" });
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      // Prepare save data
      const saveData = {
        timeline_data: {
          duration: virtualDuration,
          tracks: 2
        },
        clips: clips.map(clip => ({
          id: clip.id,
          start: clip.start,
          end: clip.end,
          duration: clip.duration,
          url: clip.url,
          title: clip.title,
          track: clip.track
        })),
        text_overlays: textOverlays,
        effects: [],
        version: 1
      };

      // Use the project ID from the clip or create a new project
      const projectId = clipId || `project_${Date.now()}`;

      await saveProject(projectId, saveData);

      toast({
        title: "Clip saved",
        description: "Your changes have been saved successfully",
      });
    } catch (error) {
      console.error('Save error:', error);
      toast({
        title: "Save failed",
        description: "Failed to save your changes",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleExport = async () => {
    try {
      setExporting(true);

      const projectId = clipId || `project_${Date.now()}`;
      const exportOptions = {
        format: "mp4",
        quality: "high",
        resolution: "1920x1080",
        fps: 30
      };

      const exportResult = await exportProject(projectId, exportOptions);

      toast({
        title: "Export started",
        description: `Export ID: ${exportResult.export_id}. Estimated time: ${exportResult.estimated_time}`,
      });

      // You could poll for export status here
      // const status = await getExportStatus(exportResult.export_id);

    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: "Export failed",
        description: "Failed to export your clip",
        variant: "destructive",
      });
    } finally {
      setExporting(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="container mx-auto py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto py-8 px-4">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="icon"
                onClick={() => navigate("/clip-results")}
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold">Clip Editor</h1>
                <p className="text-muted-foreground">
                  Edit and enhance your video clip
                </p>
              </div>
            </div>

            <div className="flex gap-2">
              <Button variant="outline" onClick={handleSave} disabled={saving}>
                {saving ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Save
              </Button>
              <Button onClick={handleExport} disabled={exporting}>
                {exporting ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                Export
              </Button>
            </div>
          </div>

          {/* Main Editor Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Video Preview */}
            <div className="lg:col-span-3 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Preview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="relative bg-black rounded-lg aspect-video">
                    <video
                      ref={videoRef}
                      src={clips[0]?.url}
                      className="w-full h-full object-contain rounded-lg"
                      style={getFilterStyle(activeFilter)}
                      controls={false}
                      onTimeUpdate={handleTimeUpdate}
                    />

                    {/* Text Overlays Preview */}
                    {textOverlays
                      .filter(
                        (overlay) =>
                          virtualTime >= overlay.startTime &&
                          virtualTime <= overlay.endTime
                      )
                      .map((overlay) => (
                        <div
                          key={overlay.id}
                          className="absolute pointer-events-none"
                          style={{
                            left: `${overlay.x}%`,
                            top: `${overlay.y}%`,
                            fontSize: `${overlay.fontSize}px`,
                            color: overlay.color,
                            fontWeight: "bold",
                            textShadow: "2px 2px 4px rgba(0,0,0,0.8)",
                            transform: "translate(-50%, -50%)",
                          }}
                        >
                          {overlay.text}
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>

              {/* Timeline */}
              <Timeline
                clips={clips}
                textOverlays={textOverlays}
                duration={virtualDuration}
                segments={timelineSegments}
                currentTime={virtualTime}
                isPlaying={isPlaying}
                onTimeChange={handleTimeChange}
                onPlay={handlePlay}
                onPause={handlePause}
                onClipUpdate={handleClipUpdate}
                onClipDelete={handleClipDelete}
                onClipAdd={handleClipAdd}
                onClipDuplicate={handleClipDuplicate}
                onClipSplit={handleClipSplit}
                onSpeedChange={handleSpeedChange}
                onTextOverlayUpdate={handleTextOverlayUpdate}
                onClipMove={handleClipMove}
                onTextOverlayDelete={handleTextOverlayDelete}
                onFilterDelete={handleFilterDelete}
              />
            </div>

            {/* Editing Tools */}
            <div className="space-y-4">
              {/* Clip Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    Clip Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Title</label>
                    <Input
                      value={clipTitle}
                      onChange={(e) => setClipTitle(e.target.value)}
                      placeholder="Enter clip title..."
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Description</label>
                    <Textarea
                      value={clipDescription}
                      onChange={(e) => setClipDescription(e.target.value)}
                      placeholder="Enter clip description..."
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Text Overlays */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Type className="h-4 w-4" />
                    Text Overlays
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Add New Text */}
                  <div className="space-y-3">
                    <Input
                      placeholder="Enter text..."
                      value={newTextOverlay.text}
                      onChange={(e) =>
                        setNewTextOverlay((prev) => ({
                          ...prev,
                          text: e.target.value,
                        }))
                      }
                    />

                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <label className="text-xs text-muted-foreground">
                          Font Size
                        </label>
                        <Slider
                          value={[newTextOverlay.fontSize]}
                          onValueChange={(value) =>
                            setNewTextOverlay((prev) => ({
                              ...prev,
                              fontSize: value[0],
                            }))
                          }
                          min={12}
                          max={48}
                          step={2}
                        />
                        <div className="text-xs text-center">
                          {newTextOverlay.fontSize}px
                        </div>
                      </div>

                      <div>
                        <label className="text-xs text-muted-foreground">
                          Color
                        </label>
                        <Input
                          type="color"
                          value={newTextOverlay.color}
                          onChange={(e) =>
                            setNewTextOverlay((prev) => ({
                              ...prev,
                              color: e.target.value,
                            }))
                          }
                          className="h-8"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <label className="text-xs text-muted-foreground">
                          Start (s)
                        </label>
                        <Input
                          type="number"
                          value={newTextOverlay.startTime}
                          onChange={(e) =>
                            setNewTextOverlay((prev) => ({
                              ...prev,
                              startTime: Number(e.target.value),
                            }))
                          }
                          min={0}
                          max={duration}
                        />
                      </div>
                      <div>
                        <label className="text-xs text-muted-foreground">
                          End (s)
                        </label>
                        <Input
                          type="number"
                          value={newTextOverlay.endTime}
                          onChange={(e) =>
                            setNewTextOverlay((prev) => ({
                              ...prev,
                              endTime: Number(e.target.value),
                            }))
                          }
                          min={0}
                          max={duration}
                        />
                      </div>
                    </div>

                    <Button
                      onClick={addTextOverlay}
                      className="w-full"
                      size="sm"
                    >
                      Add Text Overlay
                    </Button>
                  </div>

                  {/* Existing Overlays */}
                  <div className="space-y-2">
                    {textOverlays.map((overlay) => (
                      <div
                        key={overlay.id}
                        className="flex items-center justify-between p-2 border rounded"
                      >
                        <div className="flex-1">
                          <div className="text-sm font-medium truncate">
                            {overlay.text}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {overlay.startTime}s - {overlay.endTime}s
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeTextOverlay(overlay.id)}
                        >
                          ×
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Export Options */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Download className="h-4 w-4" />
                    Export Options
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Quality</label>
                    <Select defaultValue="1080p">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="4k">4K (3840x2160)</SelectItem>
                        <SelectItem value="1080p">1080p (1920x1080)</SelectItem>
                        <SelectItem value="720p">720p (1280x720)</SelectItem>
                        <SelectItem value="480p">480p (854x480)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Format</label>
                    <Select defaultValue="mp4">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="mp4">MP4</SelectItem>
                        <SelectItem value="mov">MOV</SelectItem>
                        <SelectItem value="avi">AVI</SelectItem>
                        <SelectItem value="webm">WebM</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Button
                    onClick={handleExport}
                    className="w-full"
                    disabled={exporting}
                  >
                    {exporting ? "Exporting..." : "Export Video"}
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ClipEditor;

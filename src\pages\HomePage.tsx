import React, { useState, useEffect } from "react";
import { Navigate, Link } from "react-router-dom";
import DashboardLayout from "@/components/Dashboard";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Scissors,
  Play,
  Upload,
  Plus,
  Search,
  CheckCircle,
  TrendingUp,
  Users,
  Link2,
  Check,
  ArrowUp,
  ArrowDown,
  Video,
  Zap
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  getUserSocialPlatforms,
  type UserClip,
  type UserStats,
  type SocialPlatform,
} from "@/services/userService";
import { supabase } from "@/lib/supabase";

const HomePage = () => {
  const { isAuthenticated, user, isLoading } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");
  const [searchQuery, setSearchQuery] = useState("");
  const [clips, setClips] = useState<UserClip[]>([]);
  const [stats, setStats] = useState<UserStats>({
    totalViews: 0,
    totalVideos: 0,
    totalClips: 0,
    totalSubscribers: 0,
    watchTime: 0,
    credits: 0,
  });
  const [socialPlatforms, setSocialPlatforms] = useState<SocialPlatform[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);

  useEffect(() => {
    if (isAuthenticated && user) {
      fetchAllUserData();
    }
  }, [isAuthenticated, user]);

  const fetchAllUserData = async () => {
    if (!user) return;
    setIsLoadingData(true);
    try {
      const {
        data: rawClips,
        error: clipsError,
        count,
      } = await supabase
        .from("myclip")
        .select("id, url, created_at, platform", { count: "exact" })
        .eq("user_id", user.id)
        .order("created_at", { ascending: false })
        .limit(20);

      if (clipsError) throw clipsError;

      const formattedClips: UserClip[] = (rawClips || []).map((clip) => ({
        id: clip.id,
        thumbnail: clip.url.trim().replace(/\.mp4$/, ".jpg"),
        title: `Clip from ${new Date(clip.created_at).toLocaleDateString()}`,
        status: "published",
        duration: "0:30",
        platform: clip.platform || "Cloudinary",
        views: 0,
        likes: 0,
        comments: 0,
        createdAt: new Date(clip.created_at).toLocaleDateString(),
        url: clip.url,
        vitalityScore: 75,
        videoId: 1,
        downloadCount: 0,
      }));

      setClips(formattedClips);
      setStats({
        totalViews: 43700,
        totalVideos: count || 0,
        totalClips: count || 0,
        totalSubscribers: 1250,
        watchTime: 2840,
        credits: 100,
      });

      const platformsData = await getUserSocialPlatforms();
      setSocialPlatforms(platformsData);
    } catch (error) {
      console.error("Error fetching user data:", error);
    } finally {
      setIsLoadingData(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isAuthenticated) return <Navigate to="/landing" />;

  const performanceStats = [
    {
      title: "Clips Generated",
      value: stats.totalClips.toString(),
      change: "+12%",
      trend: "up" as const,
      icon: Scissors,
      color: "text-purple-500",
    },
    {
      title: "Credits Available",
      value: stats.credits.toString(),
      change: "Available",
      trend: "neutral" as const,
      icon: Zap,
      color: "text-yellow-500",
    },
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-xl md:text-2xl font-semibold">
            Channel dashboard
          </h1>
          <p className="text-sm md:text-base text-muted-foreground">
            Track your content performance and manage your clips
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
          {performanceStats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-4 md:p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <stat.icon className={`h-4 w-4 ${stat.color}`} />
                    {stat.trend !== "neutral" && (
                      <div
                        className={`flex items-center text-xs ${
                          stat.trend === "up"
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {stat.trend === "up" ? (
                          <ArrowUp className="h-3 w-3 mr-1" />
                        ) : (
                          <ArrowDown className="h-3 w-3 mr-1" />
                        )}
                        <span className="hidden sm:inline">{stat.change}</span>
                      </div>
                    )}
                  </div>
                  <div>
                    <p className="text-lg md:text-2xl font-bold mb-1">
                      {stat.value}
                    </p>
                    <p className="text-xs md:text-sm text-muted-foreground">
                      {stat.title}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-4 md:space-y-6"
        >
          <TabsList className="grid w-full grid-cols-4 h-auto p-1">
            <TabsTrigger value="overview" className="text-xs md:text-sm py-2">
              Overview
            </TabsTrigger>
            <TabsTrigger value="content" className="text-xs md:text-sm py-2">
              Content
            </TabsTrigger>
            <TabsTrigger value="analytics" className="text-xs md:text-sm py-2">
              Analytics
            </TabsTrigger>
            <TabsTrigger value="settings" className="text-xs md:text-sm py-2">
              Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4 md:space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <h2 className="text-lg md:text-xl font-semibold">
                    Latest uploads
                  </h2>
                  <Link to="/clip-results">
                    <Button variant="outline" size="sm">
                      View all
                    </Button>
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
                  {clips.length > 0 ? (
                    clips.slice(0, 4).map((clip) => (
                      <Card
                        key={clip.id}
                        className="group hover:shadow-md transition-all"
                      >
                        <div className="relative">
                          <img
                            src={clip.thumbnail}
                            alt={clip.title}
                            className="w-full h-32 object-cover rounded-t-lg"
                          />
                          <Badge className="absolute top-2 right-2 text-xs">
                            {clip.duration}
                          </Badge>
                        </div>
                        <CardContent className="p-3">
                          <h3 className="font-medium text-sm mb-1 line-clamp-2">
                            {clip.title}
                          </h3>
                          <p className="text-xs text-muted-foreground">
                            {clip.views} views • {clip.createdAt}
                          </p>
                        </CardContent>
                      </Card>
                    ))
                  ) : (
                    <div className="col-span-full text-center py-8">
                      <Video className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium mb-2">No clips yet</h3>
                      <p className="text-muted-foreground mb-4">
                        Start creating clips to see them here
                      </p>
                      <Link to="/smart-clipper">
                        <Button>Create First Clip</Button>
                      </Link>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="content">
            <Card>
              <CardHeader>
                <CardTitle>Content Management</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Content management features coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics">
            <Card>
              <CardHeader>
                <CardTitle>Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Analytics dashboard coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Settings panel coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default HomePage;

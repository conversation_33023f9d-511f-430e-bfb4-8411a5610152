import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import {
  VideoIcon,
  ArrowRight,
  Play,
  Scissors,
  Sparkles,
  Zap,
  CheckCircle,
  Star,
  Users,
  TrendingUp,
  Shield,
  Clock
} from "lucide-react";

const Index = () => {
  const [currentFeature, setCurrentFeature] = useState(0);

  const features = [
    {
      title: "Smart Clipper",
      description: "AI-powered video clipping that finds the most engaging moments",
      icon: Scissors,
      color: "text-blue-500"
    },
    {
      title: "Video Editor",
      description: "Professional editing tools with real-time preview",
      icon: VideoIcon,
      color: "text-purple-500"
    },
    {
      title: "Social Integration",
      description: "Direct publishing to all major social platforms",
      icon: TrendingUp,
      color: "text-green-500"
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <main className="flex-1">
        {/* Tesla-Inspired Hero Section */}
        <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
          {/* Background gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-brand-purple/5" />

          {/* Animated background elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-brand-purple/10 rounded-full blur-3xl animate-pulse-opacity" />
            <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-brand-blue/10 rounded-full blur-3xl animate-pulse-opacity" style={{ animationDelay: '2s' }} />
          </div>

          <div className="relative z-10 container mx-auto px-4 text-center">
            {/* Main heading with Tesla-style typography */}
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-light mb-8 tracking-tight">
              <span className="block">Transform</span>
              <span className="block gradient-text font-medium">Every Video</span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto font-light leading-relaxed">
              AI-powered video editing that turns long-form content into viral clips.
              Professional results in minutes, not hours.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
              <Link to="/login">
                <Button
                  size="lg"
                  className="bg-gradient-purple-blue hover:opacity-90 transition-all duration-300 px-12 py-6 text-lg font-medium rounded-full shadow-2xl hover:shadow-purple-500/25"
                >
                  Get Started
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link to="#features">
                <Button
                  size="lg"
                  variant="ghost"
                  className="px-12 py-6 text-lg font-medium rounded-full border border-border/50 hover:border-primary/50 transition-all duration-300"
                >
                  <Play className="mr-2 h-5 w-5" />
                  Watch Demo
                </Button>
              </Link>
            </div>

            {/* Feature showcase */}
            <div className="max-w-2xl mx-auto">
              <div className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8 transition-all duration-500">
                <div className="flex items-center justify-center mb-4">
                  {React.createElement(features[currentFeature].icon, {
                    className: `h-12 w-12 ${features[currentFeature].color}`
                  })}
                </div>
                <h3 className="text-2xl font-semibold mb-3">{features[currentFeature].title}</h3>
                <p className="text-muted-foreground text-lg">{features[currentFeature].description}</p>
              </div>

              {/* Feature indicators */}
              <div className="flex justify-center mt-6 space-x-2">
                {features.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentFeature(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${index === currentFeature
                      ? 'bg-primary scale-125'
                      : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'
                      }`}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Scroll indicator */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div className="w-6 h-10 border-2 border-muted-foreground/30 rounded-full flex justify-center">
              <div className="w-1 h-3 bg-muted-foreground/50 rounded-full mt-2 animate-pulse" />
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-32 px-4 bg-card/20">
          <div className="container mx-auto">
            {/* Section header */}
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-6xl font-light mb-6 tracking-tight">
                Everything You Need
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto font-light">
                Professional video editing tools powered by AI, designed for creators who demand excellence
              </p>
            </div>

            {/* Feature grid */}
            <div className="grid lg:grid-cols-3 gap-8 mb-20">
              <FeatureCard
                title="Smart Clipper"
                description="AI analyzes your content to automatically extract the most engaging segments, saving hours of manual editing."
                icon={<Scissors className="h-8 w-8" />}
                features={["Auto-highlight detection", "Viral moment analysis", "Multi-platform optimization"]}
                linkTo="/smart-clipper"
              />

              <FeatureCard
                title="Professional Editor"
                description="Full-featured video editor with timeline, effects, and real-time collaboration tools."
                icon={<VideoIcon className="h-8 w-8" />}
                features={["Timeline editing", "Real-time preview", "Advanced effects"]}
                linkTo="/video-editor"
              />

              <FeatureCard
                title="Social Integration"
                description="Direct publishing to YouTube, TikTok, Instagram, and more with platform-specific optimization."
                icon={<TrendingUp className="h-8 w-8" />}
                features={["One-click publishing", "Platform optimization", "Analytics tracking"]}
                linkTo="/social-integration"
              />
            </div>

            {/* Stats section */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold gradient-text">10M+</div>
                <div className="text-muted-foreground">Videos Processed</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold gradient-text">500K+</div>
                <div className="text-muted-foreground">Active Creators</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold gradient-text">95%</div>
                <div className="text-muted-foreground">Time Saved</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold gradient-text">4.9★</div>
                <div className="text-muted-foreground">User Rating</div>
              </div>
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section className="py-32 px-4">
          <div className="container mx-auto">
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-6xl font-light mb-6 tracking-tight">
                Simple. <span className="gradient-text">Powerful.</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto font-light">
                Three steps to transform your content into viral clips
              </p>
            </div>

            <div className="max-w-6xl mx-auto">
              <div className="grid md:grid-cols-3 gap-12">
                {/* Step 1 */}
                <div className="text-center group">
                  <div className="relative mb-8">
                    <div className="w-20 h-20 mx-auto rounded-full bg-gradient-purple-blue flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                      <VideoIcon className="h-10 w-10 text-white" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary rounded-full flex items-center justify-center text-sm font-bold text-white">
                      1
                    </div>
                  </div>
                  <h3 className="text-2xl font-semibold mb-4">Upload Your Video</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Simply drag and drop your long-form content or paste a URL from YouTube, TikTok, or any supported platform.
                  </p>
                </div>

                {/* Step 2 */}
                <div className="text-center group">
                  <div className="relative mb-8">
                    <div className="w-20 h-20 mx-auto rounded-full bg-gradient-purple-blue flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                      <Sparkles className="h-10 w-10 text-white" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary rounded-full flex items-center justify-center text-sm font-bold text-white">
                      2
                    </div>
                  </div>
                  <h3 className="text-2xl font-semibold mb-4">AI Processing</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Our AI analyzes your content, identifies key moments, and automatically creates engaging clips optimized for each platform.
                  </p>
                </div>

                {/* Step 3 */}
                <div className="text-center group">
                  <div className="relative mb-8">
                    <div className="w-20 h-20 mx-auto rounded-full bg-gradient-purple-blue flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                      <Zap className="h-10 w-10 text-white" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary rounded-full flex items-center justify-center text-sm font-bold text-white">
                      3
                    </div>
                  </div>
                  <h3 className="text-2xl font-semibold mb-4">Publish & Share</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Download your clips or publish directly to social media with one click. Track performance with built-in analytics.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Social Proof Section */}
        <section className="py-32 px-4 bg-card/20">
          <div className="container mx-auto text-center">
            <h2 className="text-4xl md:text-5xl font-light mb-6 tracking-tight">
              Trusted by <span className="gradient-text">Creators Worldwide</span>
            </h2>
            <p className="text-xl text-muted-foreground mb-16 max-w-3xl mx-auto font-light">
              Join thousands of content creators who have transformed their workflow with SmartClips
            </p>

            <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8">
                <div className="flex justify-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-500 fill-current" />
                  ))}
                </div>
                <p className="text-muted-foreground mb-6 italic">
                  "SmartClips cut my editing time by 90%. What used to take hours now takes minutes."
                </p>
                <div className="font-semibold">Sarah Chen</div>
                <div className="text-sm text-muted-foreground">YouTube Creator, 2M subscribers</div>
              </div>

              <div className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8">
                <div className="flex justify-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-500 fill-current" />
                  ))}
                </div>
                <p className="text-muted-foreground mb-6 italic">
                  "The AI knows exactly which moments will go viral. My engagement is up 300%."
                </p>
                <div className="font-semibold">Marcus Rodriguez</div>
                <div className="text-sm text-muted-foreground">TikTok Influencer, 5M followers</div>
              </div>

              <div className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8">
                <div className="flex justify-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-500 fill-current" />
                  ))}
                </div>
                <p className="text-muted-foreground mb-6 italic">
                  "Professional results without the professional price tag. Game changer!"
                </p>
                <div className="font-semibold">Alex Thompson</div>
                <div className="text-sm text-muted-foreground">Marketing Agency Owner</div>
              </div>
            </div>
          </div>
        </section>

        {/* Final CTA Section */}
        <section className="py-32 px-4 relative overflow-hidden">
          {/* Background elements */}
          <div className="absolute inset-0 bg-gradient-to-r from-brand-purple/10 to-brand-blue/10" />
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-brand-purple/5 rounded-full blur-3xl" />
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-brand-blue/5 rounded-full blur-3xl" />

          <div className="relative z-10 container mx-auto text-center">
            <h2 className="text-5xl md:text-7xl font-light mb-8 tracking-tight">
              Start Creating
              <span className="block gradient-text font-medium">Today</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-12 font-light leading-relaxed">
              Join the revolution in video creation. Transform your content, grow your audience,
              and save countless hours with AI-powered editing.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
              <Link to="/login">
                <Button
                  size="lg"
                  className="bg-gradient-purple-blue hover:opacity-90 transition-all duration-300 px-12 py-6 text-lg font-medium rounded-full shadow-2xl hover:shadow-purple-500/25"
                >
                  Get Started Free
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link to="/contact">
                <Button
                  size="lg"
                  variant="ghost"
                  className="px-12 py-6 text-lg font-medium rounded-full border border-border/50 hover:border-primary/50 transition-all duration-300"
                >
                  Contact Sales
                </Button>
              </Link>
            </div>

            {/* Trust indicators */}
            <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                <span>Enterprise Security</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span>24/7 Support</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                <span>99.9% Uptime</span>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-card/30 backdrop-blur-sm py-16 px-4 border-t border-border/30">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-4 gap-8 mb-12">
            {/* Brand */}
            <div className="md:col-span-2">
              <Link to="/" className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 rounded-lg bg-gradient-purple-blue flex items-center justify-center">
                  <VideoIcon className="h-6 w-6 text-white" />
                </div>
                <span className="font-bold text-2xl gradient-text">
                  SmartClips
                </span>
              </Link>
              <p className="text-muted-foreground mb-6 max-w-md leading-relaxed">
                Transform your video content with AI-powered editing tools.
                Create viral clips, professional edits, and engaging content in minutes.
              </p>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Users className="h-4 w-4" />
                  <span>500K+ Creators</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Star className="h-4 w-4 text-yellow-500" />
                  <span>4.9/5 Rating</span>
                </div>
              </div>
            </div>

            {/* Tools */}
            <div>
              <h3 className="font-semibold mb-4 text-lg">Tools</h3>
              <ul className="space-y-3">
                <li>
                  <Link
                    to="/smart-clipper"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Smart Clipper
                  </Link>
                </li>
                <li>
                  <Link
                    to="/video-editor"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Video Editor
                  </Link>
                </li>
                <li>
                  <Link
                    to="/analytics"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Analytics
                  </Link>
                </li>
                <li>
                  <Link
                    to="/social-integration"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Social Integration
                  </Link>
                </li>
              </ul>
            </div>

            {/* Company */}
            <div>
              <h3 className="font-semibold mb-4 text-lg">Company</h3>
              <ul className="space-y-3">
                <li>
                  <Link
                    to="/about"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    About Us
                  </Link>
                </li>
                <li>
                  <Link
                    to="/contact"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Contact
                  </Link>
                </li>
                <li>
                  <Link
                    to="/privacy"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link
                    to="/payment"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Pricing
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className="pt-8 border-t border-border/30 flex flex-col md:flex-row justify-between items-center">
            <p className="text-muted-foreground text-sm">
              © {new Date().getFullYear()} SmartClips. All rights reserved.
            </p>
            <div className="flex items-center gap-6 mt-4 md:mt-0">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Shield className="h-4 w-4" />
                <span>SOC 2 Compliant</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <CheckCircle className="h-4 w-4" />
                <span>GDPR Ready</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

// Feature Card Component
const FeatureCard = ({
  title,
  description,
  icon,
  features,
  linkTo,
}: {
  title: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  linkTo: string;
}) => {
  return (
    <div className="group relative">
      <div className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8 h-full hover:border-primary/30 transition-all duration-300 hover:shadow-lg hover:shadow-primary/5">
        {/* Icon */}
        <div className="w-16 h-16 rounded-xl bg-gradient-purple-blue flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <div className="text-white">
            {icon}
          </div>
        </div>

        {/* Content */}
        <h3 className="text-2xl font-semibold mb-4">{title}</h3>
        <p className="text-muted-foreground mb-6 leading-relaxed">{description}</p>

        {/* Features list */}
        <ul className="space-y-2 mb-8">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center gap-3 text-sm">
              <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
              <span className="text-muted-foreground">{feature}</span>
            </li>
          ))}
        </ul>

        {/* CTA */}
        <Link
          to={linkTo}
          className="inline-flex items-center text-primary hover:text-primary/80 font-medium group-hover:translate-x-1 transition-all duration-300"
        >
          Explore {title}
          <ArrowRight className="ml-2 h-4 w-4" />
        </Link>
      </div>
    </div>
  );
};

export default Index;

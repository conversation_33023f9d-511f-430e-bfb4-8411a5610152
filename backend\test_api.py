#!/usr/bin/env python3
"""
Test script for SmartClips backend API functionality
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test the basic health check endpoint"""
    print("🔍 Testing health check endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data['message']}")
            return True
        else:
            print(f"❌ Health check failed with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {str(e)}")
        return False

def test_video_processing_functions():
    """Test video processing function imports"""
    print("\n🔍 Testing video processing functions...")
    try:
        import video_processing
        
        # Test that our new functions exist
        required_functions = [
            'segment_transcript',
            'transcribe_video', 
            'clip_video_from_text'
        ]
        
        missing_functions = []
        for func_name in required_functions:
            if not hasattr(video_processing, func_name):
                missing_functions.append(func_name)
        
        if missing_functions:
            print(f"❌ Missing functions: {missing_functions}")
            return False
        else:
            print("✅ All required video processing functions available")
            return True
            
    except Exception as e:
        print(f"❌ Video processing test error: {str(e)}")
        return False

def test_ai_segmentation_logic():
    """Test the new AI segmentation logic"""
    print("\n🔍 Testing AI segmentation logic...")
    try:
        import video_processing
        
        # Test with mock data
        mock_transcript = "This is a test transcript. It has multiple sentences. Some are more engaging than others!"
        mock_timestamps = [
            {"word": "This", "start": 0.0, "end": 0.5},
            {"word": "is", "start": 0.5, "end": 0.7},
            {"word": "a", "start": 0.7, "end": 0.8},
            {"word": "test", "start": 0.8, "end": 1.2},
            {"word": "transcript.", "start": 1.2, "end": 2.0},
            {"word": "It", "start": 2.5, "end": 2.7},
            {"word": "has", "start": 2.7, "end": 3.0},
            {"word": "multiple", "start": 3.0, "end": 3.8},
            {"word": "sentences.", "start": 3.8, "end": 4.5},
        ]
        
        # Test basic segmentation (without AI)
        segments = video_processing.segment_transcript(
            mock_transcript, 
            mock_timestamps, 
            min_duration=2.0, 
            max_duration=10.0, 
            refine_with_ai=False
        )
        
        if segments and len(segments) > 0:
            print(f"✅ Basic segmentation working: {len(segments)} segments created")
            print(f"   Sample segment: {segments[0]['text'][:50]}...")
            return True
        else:
            print("❌ Basic segmentation failed: no segments created")
            return False
            
    except Exception as e:
        print(f"❌ AI segmentation test error: {str(e)}")
        return False

def test_database_connection():
    """Test database connection and table creation"""
    print("\n🔍 Testing database connection...")
    try:
        from database import engine, SessionLocal
        from models import Base, User, Video, VideoClip
        
        # Test database connection
        with SessionLocal() as db:
            # Try a simple query
            user_count = db.query(User).count()
            print(f"✅ Database connection successful. Users in DB: {user_count}")
            return True
            
    except Exception as e:
        print(f"❌ Database connection error: {str(e)}")
        return False

def test_environment_config():
    """Test environment configuration"""
    print("\n🔍 Testing environment configuration...")
    try:
        import os
        from dotenv import load_dotenv
        load_dotenv()
        
        required_configs = {
            'OPENAI_API_KEY': 'OpenAI API',
            'CLOUDINARY_CLOUD_NAME': 'Cloudinary',
            'SECRET_KEY': 'JWT Secret',
            'DATABASE_URL': 'Database'
        }
        
        missing_configs = []
        for key, name in required_configs.items():
            if not os.getenv(key):
                missing_configs.append(name)
        
        if missing_configs:
            print(f"⚠️  Missing configurations: {', '.join(missing_configs)}")
            print("   Some features may not work properly")
        else:
            print("✅ All required configurations present")
        
        return len(missing_configs) == 0
        
    except Exception as e:
        print(f"❌ Environment config test error: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting SmartClips Backend Diagnostic Tests\n")
    
    tests = [
        test_health_check,
        test_video_processing_functions,
        test_ai_segmentation_logic,
        test_database_connection,
        test_environment_config
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        time.sleep(0.5)  # Small delay between tests
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! SmartClips backend is ready to use.")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    main()

import { supabase } from "@/lib/supabase";
import { toast } from "@/hooks/use-toast";

export const deductSecondsFromUser = async (
  userId: string,
  secondsToDeduct: number,
): Promise<boolean> => {
  try {
    // Step 1: Fetch the user's current second balance
    const { data: profile, error: fetchError } = await supabase
      .from("profiles")
      .select("second, credits")
      .eq("id", userId)
      .single();

    console.log("Fetched user profile in the user.ts file:", profile);

    if (fetchError || !profile) {
      console.error(
        "Deduction error: Could not fetch user profile.",
        fetchError,
      );
      toast({
        title: "Billing Error",
        description: "Could not find your profile to update your balance.",
        variant: "destructive",
      });
      return false;
    }

    const currentSeconds = profile.second;
    const currentCredits = profile.credits;

    if (currentSeconds < secondsToDeduct) {
      toast({
        title: "Insufficient Credits",
        description:
          `You need ${secondsToDeduct} seconds for these clips, but you only have ${currentSeconds}. Please top up your account.`,
        variant: "destructive",
      });
      // This error will be caught and will stop the onSubmit process
      throw new Error("Insufficient credits");
    }

    const creditsToDeduct = secondsToDeduct / 60.0;

    // 4. Calculate the new balances by SUBTRACTING the calculated costs
    const newSeconds = currentSeconds - secondsToDeduct;
    const newCredits = currentCredits - creditsToDeduct;

    // Step 3: Update the user's profile with the new balance
    const { error: updateError } = await supabase
      .from("profiles")
      .update({
        second: newSeconds,
        credits: newCredits,
      })
      .eq("id", userId);

    if (updateError) {
      console.error(
        "Deduction error: Could not update user balance.",
        updateError,
      );
      toast({
        title: "Billing Error",
        description: "An error occurred while updating your balance.",
        variant: "destructive",
      });
      return false;
    }

    console.log(
      `Successfully deducted ${secondsToDeduct} seconds. New balance: ${
        newCredits.toFixed(2)
      } credits (${newSeconds} seconds)`,
    );
    return true;
  } catch (error) {
    if (
      !(error instanceof Error &&
        error.message.includes("Insufficient credits"))
    ) {
      console.error("An unexpected error occurred during deduction:", error);
      toast({
        title: "Deduction Failed",
        description: "An unexpected error occurred. Please contact support.",
        variant: "destructive",
      });
    }
    return false;
  }
};
